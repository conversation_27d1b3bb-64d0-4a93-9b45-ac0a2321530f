{"name": "api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@turf/boolean-intersects": "^7.2.0", "@turf/turf": "^5.1.6", "express": "^4.17.1", "fast-xml-parser": "^5.2.5", "geodesy": "^1.1.3", "gm": "^1.23.1", "https-proxy-agent": "^7.0.2", "jsonwebtoken": "^9.0.2", "minio": "^7.0.14", "mkdirp": "^0.5.1", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.11", "papaparse": "^5.4.1", "pg-promise": "^10.7.4", "proj4": "^2.9.0", "proper-lockfile": "^4.1.2", "redis": "^2.8.0", "request": "^2.88.2", "sharp": "^0.34.2", "url": "^0.11.0", "uuid": "^3.4.0", "valid-url": "^1.0.9", "wasm-vips": "^0.0.13", "write-file-atomic": "^5.0.1"}}