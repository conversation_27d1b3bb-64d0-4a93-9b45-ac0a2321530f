const geofunctions = require('./geofunctions')
const nearmap = require('./nearmap')
const db = require('./db')
const turf = require('@turf/turf');

const checkNearMapCache = async (bounds, date) => {
    const turfBounds = geofunctions.convertBoundsStringToTurfBounds(bounds)
    const boundsPoly = turf.bboxPolygon(turfBounds)
    
    const p1= await db.allCached(boundsPoly)
    let filteredArray = p1.filter(item => {
        let imgDate = new Date(item.date);
        let formattedDate = imgDate.getFullYear() + "-" + (imgDate.getMonth()+1).toString().padStart(2, '0') + "-" + imgDate.getDate().toString().padStart(2, '0');
        console.log(formattedDate)
        return formattedDate === date;
      });
    return filteredArray
}


let bounds = '-70.0058328282595,41.785776631673,-70.00440486552715,41.786993475196894'
let date = '2023-04-08'

checkNearMapCache(bounds, date).then(function (result) {
    console.log(result)
});