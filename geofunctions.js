const turf = require('@turf/turf')


const getBoundsArea = (bounds) => {
  const boundsPoly = convertBoundsToTurfPoly(bounds);
  const area = turf.area(boundsPoly);
  return {
    area: area
  }
}

const convertBoundsToTurfPoly = (bounds) => {
  const boundsArray = bounds.split(",")
  return turf.bboxPolygon([boundsArray[0], boundsArray[1], boundsArray[2], boundsArray[3]])
}

const convertBoundsStringToTurfBounds = (bounds) => {
  const boundsArray = bounds.split(",")
  return ([parseFloat(boundsArray[0]), parseFloat(boundsArray[1]), parseFloat(boundsArray[2]), parseFloat(boundsArray[3])])
}




//used by coverage api which requires LON,LAT
const convertCoordsToNearMap = (feature) => {
  const coordsArray = feature.geometry.coordinates
  var nearpMapCoords = "";
  for (i = 0; i < coordsArray[0].length; i++) {
    if (i != 0) nearpMapCoords += ","
    nearpMapCoords += coordsArray[0][i][0]
    nearpMapCoords += ","
    nearpMapCoords += coordsArray[0][i][1]
  }
  return nearpMapCoords;
}

//used by image api
const convertBoundsToNearMap = (bounds) => {
  return bounds[1] + "," + bounds[0] + "," + bounds[3] + "," + bounds[2]
}

const rectangleGrid = (bbox, cellWidth, cellHeight, options) => {
  if (options === void 0) {
    options = {};
  }
  // Containers
  var results = [];
  var west = bbox[0];
  var south = bbox[1];
  var east = bbox[2];
  var north = bbox[3];
  var xFraction = cellWidth / (turf.distance([west, south], [east, south], options));
  var cellWidthDeg = xFraction * (east - west);
  var yFraction = cellHeight / (turf.distance([west, south], [west, north], options));
  var cellHeightDeg = yFraction * (north - south);
  // rows & columns
  var bboxWidth = (east - west);
  var bboxHeight = (north - south);
  var columns = Math.floor(bboxWidth / cellWidthDeg);
  var rows = Math.floor(bboxHeight / cellHeightDeg);
  // if the grid does not fill the bbox perfectly, center it.
  var deltaX = (bboxWidth - columns * cellWidthDeg) / 2;
  var deltaY = (bboxHeight - rows * cellHeightDeg) / 2;
  // iterate over columns & rows
  
  var currentY = north + deltaY;
  for (var row = 0; row < rows; row++) {
    var currentX = west + deltaX;
    for (var column = 0; column < columns; column++) {
      var cellPoly = turf.polygon([
        [
          [currentX, currentY],
          [currentX, currentY - cellHeightDeg],
          [currentX + cellWidthDeg, currentY - cellHeightDeg],
          [currentX + cellWidthDeg, currentY],
          [currentX, currentY],
        ]
      ], options.properties);
      if (options.mask) {
        if (turf.boolean - intersects(options.mask, cellPoly)) {
          results.push(cellPoly);
        }
      } else {
        results.push(cellPoly);
      }
      currentX += cellWidthDeg;
    }
    currentY -= cellHeightDeg;
  }
  return turf.featureCollection(results);
}
const tileToLonLat = (z, x, y) => {
    const n = Math.pow(2, z);
    const lon = (x / n) * 360 - 180;
    const latRad = Math.atan(Math.sinh(Math.PI * (1 - (2 * y) / n)));
    const lat = latRad * 180.0 / Math.PI;
    return [lon, lat];
};

const isPointInBounds = (point, bounds) => {
    const pt = turf.point(point);
    const poly = turf.polygon([bounds]);
    return turf.booleanPointInPolygon(pt, poly);
};

const tileToBoundingBox = (z, x, y) => {
    const northWest = tileToLonLat(z, x, y);
    const southEast = tileToLonLat(z, x + 1, y + 1);
    const boundingBox = [
        [
            northWest,
            [southEast[0], northWest[1]],
            southEast,
            [northWest[0], southEast[1]],
            northWest
        ]
    ];
    return turf.polygon(boundingBox);
};

module.exports = {
  getBoundsArea,
  convertBoundsToTurfPoly,
  convertCoordsToNearMap,
  convertBoundsStringToTurfBounds,
  rectangleGrid,
   tileToLonLat,
   isPointInBounds,
   tileToBoundingBox,
  convertBoundsToNearMap
}