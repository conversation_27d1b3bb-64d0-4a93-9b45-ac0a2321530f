const pgp = require('pg-promise')({

});

const turf = require('@turf/turf')
const cn = {
  user: 'pictometry',
  host: '127.0.0.1',
  database: 'pictometry',
  password: 'Y^<Ta5C+H(d|$,Gj',
  port: 5432
}

const pdb = pgp(cn)

const isCustomer = (accessCode) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT * FROM users WHERE users_accesscode = $1::text limit 1", [accessCode])
      .then(function (data) {
        resolve(data)
      })
      .catch(function (error) {
        reject(error)
      });
  })

}

const copyTile = async (accesscode,url) => {
  return new Promise(async function (resolve, reject) {
    pdb.any("select tiles_id,tiles_accesscode from tiles where tiles_url = $1", [url])
      .then(async function (tiles) {
        for(let tile of tiles)
        {
            let array = tile.tiles_accesscode.split(",");
            if (array.indexOf(accesscode) === -1) {
              array.push(accesscode)
              await addTileAccess(tile.tiles_id, array.join())
            }
            
        }
        resolve()
      })
      .catch(function (error) {
        reject(error)
      });
  })
}

const userStatus = (accessCode) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT * FROM users WHERE users_accesscode = $1::text limit 1", [accessCode])
      .then(function (data) {
        resolve(data)
      })
      .catch(function (error) {
        reject(error)
      });
  })

}

const checkCachePic = (bounds, dt) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT tiles_id, tiles_accesscode, tiles_imagery_date AS date, tiles_provider AS provider FROM tiles WHERE ST_Equals(tiles_bound, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326)) AND tiles_imagery_date=$2 AND tiles_provider='pictometry'", [bounds,dt])
    .then(function (data) {  
      resolve(data)
    })
    .catch(function (error) {
      reject(error)
    });
  })
}

const checkCacheNear = (bounds, dt) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT tiles_id, tiles_accesscode, tiles_imagery_date AS date, tiles_provider AS provider FROM tiles WHERE ST_Within(tiles_bound, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326)) AND tiles_imagery_date=$2 AND tiles_provider='nearmap'", [bounds,dt])
    .then(function (data) {  
      resolve(data)
    })
    .catch(function (error) {
      reject(error)
    });
  })
}

const allCached = (bounds) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT tiles_id as id, ST_AsGeoJSON(tiles_bound) as bounds, (st_area(st_intersection(tiles_bound, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326)))/st_area(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326))) as coverage, tiles_accesscode, tiles_url, tiles_imagery_date AS date, tiles_provider AS provider FROM tiles WHERE st_intersects(tiles_bound, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326))", [bounds.geometry,bounds.geometry, bounds.geometry])
        .then(function (data) {  
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
  })
}

const urlCached = (url) => {
  console.log(url)
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT tiles_id as id, ST_AsGeoJSON(tiles_bound) as bounds, tiles_accesscode, tiles_url, tiles_imagery_date AS date, tiles_provider AS provider FROM tiles WHERE tiles_url like $1",['%' + url + '%'])
    .then(function (data) {  
      resolve(data)
    })
    .catch(function (error) {
      reject(error)
    });
  })
}


const cachedStatus = (bounds, dt) => {
  return new Promise(function (resolve, reject) {
  
    const date = dt || 0;
    
    if (date != 0) {
      pdb.any("SELECT ST_AsGeoJSON(tiles_bound), tiles_accesscode, tiles_url, tiles_imagery_date, tiles_provider, tiles_cost FROM tiles WHERE ST_Contains(tiles_bound, ST_Dilate(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326),0.90)) AND tiles_imagery_date=$2", [bounds.geometry,date])
        .then(function (data) {
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
    } else {
      pdb.any("SELECT ST_AsGeoJSON(tiles_bound), tiles_accesscode, tiles_url, tiles_imagery_date, tiles_provider, tiles_cost FROM tiles WHERE ST_Contains(tiles_bound, ST_Dilate(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326),0.90))", [bounds.geometry])
        .then(function (data) {  
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
    }
  })

}

const updatePath = (id, path) => {
  return new Promise(function (resolve, reject) {
    pdb.one('UPDATE tiles SET tiles_url=$1 WHERE tiles_id=$2 RETURNING tiles_id', [path, id])
      .then(data => {
        resolve()
      })
      .catch(error => {
        console.log(error)
        reject()
      });
  })
}

const addTile = (accesscode, path, dt, bounds, poly, provider, cost) =>
{
    return new Promise(function (resolve, reject) {
      pdb.one('INSERT INTO tiles(tiles_bounds_text, tiles_accesscode, tiles_url, tiles_imagery_date, tiles_bound, tiles_hit_bound, tiles_provider, tiles_cost) VALUES($1,$2,$3,$4,ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($5)), 4326), ST_Dilate(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($6)), 4326),0.25), $7, $8) RETURNING tiles_id', [bounds,accesscode,path,dt,poly,poly,provider,cost])
        .then(data => {
          resolve({
            error: false,
            id: data.tiles_id
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: error
          })
        });
    })
}

const addTile2 = (accesscode, path, dt, bounds, poly, provider, cost) =>
{
    return new Promise(function (resolve, reject) {
      pdb.one('INSERT INTO tiles(tiles_bounds_text, tiles_accesscode, tiles_url, tiles_imagery_date, tiles_bound, tiles_hit_bound, tiles_provider, tiles_cost, tiles_imported) VALUES($1,$2,$3,$4,ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($5)), 4326), ST_Dilate(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($6)), 4326),0.25), $7, $8, true) RETURNING tiles_id', [bounds,accesscode,path,dt,poly,poly,provider,cost])
        .then(data => {
          resolve({
            error: false,
            id: data.tiles_id
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: error
          })
        });
    })
}





const logAccess =  (tileId, isCached, selfCached,  bid, accesscode, cost, mid) => {
  var accessType = isCached == true ? selfCached == true ? 0 : 2 : 1
  return new Promise(function (resolve, reject) {
    pdb.none("INSERT INTO logs (logs_tiles_id, logs_access_type, logs_accesscode, logs_bid, logs_cost, logs_mapid) VALUES($1,$2,$3,$4,$5,$6)", [tileId, accessType, accesscode, bid, cost,mid])
        .then(() => {
          resolve({
            error: false
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: true
          })
        });
  })
}

const logAccessClick =  (tileId, bid, accesscode, cost, mid) => {
  var accessType = 3
  return new Promise(function (resolve, reject) {
    pdb.none("INSERT INTO logs (logs_tiles_id, logs_access_type, logs_accesscode, logs_bid, logs_cost, logs_mapid) VALUES($1,$2,$3,$4,$5,$6)", [tileId, accessType, accesscode, bid, cost,mid])
        .then(() => {
          resolve({
            error: false
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: true
          })
        });
  })
}

const addTileAccess = (tileID, accessCode) => {
  return new Promise(function (resolve, reject) {
    pdb.one('UPDATE tiles SET tiles_accesscode=$1 WHERE tiles_id=$2 RETURNING tiles_id', [accessCode, tileID])
      .then(data => {
        resolve(data)
      })
      .catch(error => {
        console.log(error)
        reject()
      });
  })

}
const getTile = (tileID) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT ST_AsGeoJSON(tiles_bound) as t_bounds, tiles_accesscode, tiles_url, tiles_imagery_date, tiles_provider, tiles_cost from tiles where tiles_id=$1", [tileID])
        .then(function (data) {  
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
  })
}

const getAllTiles = () => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT * from tiles")
        .then(function (data) {  
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
  })
}
const getQuota = (accessCode) => {
  return new Promise(function (resolve, reject) {
    const pr1 = pdb.any("SELECT SUM(users_searches) as allocation FROM users WHERE users_accesscode = $1::text", [accessCode])
    const pr2 = pdb.any("SELECT SUM(logs_cost) as usage FROM logs WHERE logs_accesscode = $1::text AND (logs_access_type=1 OR logs_access_type=2)", [accessCode])
    Promise.all([pr1, pr2]).then(values => {
        resolve(values)
      })
      .catch(function (error) {
        reject(error)
      });
  })
}


const addQuota = (accessCode, password, quota, force, ip) => {
  return new Promise(function (resolve, reject) {
    var forcedownload = force || 0
    var quotamount = quota || 0
    if (password == 'Muj56f44f9') {
      pdb.none('INSERT INTO users (users_accesscode, users_searches, users_ip, users_forcedownload) VALUES ($1,$2,$3,$4)', [accessCode, quotamount, ip, forcedownload])
        .then(() => {
          resolve({
            error: false
          })
        })
        .catch(error => {
          reject({
            error: true
          })
        });
    } else
      reject({
        error: true
      })
  })
}
const addbypass = async (accessCode,password) => {
  try {
      // Check if accessCode exists
      if(password != 'Muj56f44f9')
        return {
          status: 'Error in addbypass function: Wrong password'
        }

      const exists = await pdb.oneOrNone('SELECT id FROM nearmap_accesscode_bypass WHERE accesscode = $1', [accessCode]);

      if (exists) {
          // If accessCode exists, delete it
          await pdb.none('DELETE FROM nearmap_accesscode_bypass WHERE accesscode = $1', [accessCode]);
          console.log(`Removed accessCode: ${accessCode}`);
          return {
              status: `Removed accessCode: ${accessCode}`
          }
      } else {
          // If accessCode doesn't exist, insert it
          await pdb.none('INSERT INTO nearmap_accesscode_bypass (accesscode) VALUES ($1)', [accessCode]);
          console.log(`Added accessCode: ${accessCode}`);
          return {
              status: `Added accessCode: ${accessCode}`
          }
      }
  } catch (error) {
      console.error('Error in addbypass function:', error);
      return {
          status: 'Error in addbypass function'
      }
  }
};

const logNearMap = async (zoom, date, jobID, accessCode, worker, bid, cost, mid, uncompressedSize, url, area) => {
  try {
      // Convert date from YYYYMMDD format to YYYY-MM-DD format
      const formattedDate = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;

      // Prepare the SQL statement for inserting data
      const insertSQL = `
          INSERT INTO nearmap_image_download_log 
          (zoom, date, jobID, accessCode, worker, bid, cost, mid, uncompressed_size, download_url, area)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `;

      // Execute the SQL statement with values
      await pdb.none(insertSQL, [zoom, formattedDate, jobID, accessCode, worker, bid, cost, mid, uncompressedSize, url, area]);
      
      console.log('Log entry successfully added.');
  } catch (error) {
      console.error('Error logging NearMap data:', error);
  }
};

const checkBypass = async (accessCode) => {
  try {
      // Check if accessCode exists
      const exists = await pdb.oneOrNone('SELECT id FROM nearmap_accesscode_bypass WHERE accesscode = $1', [accessCode]);
      if(exists)
        return true
      else
        return false
  } catch (error) {
      console.error('Error in checkBypass function:', error);
      return false
  }
};




const logEagleViewAccess = (accesscode, layer, zoom, x, y) => {
   return new Promise(function (resolve, reject) {
       pdb.none('INSERT INTO eagleview_access_log(accesscode, layer, zoom, x, y) VALUES($1, $2, $3, $4, $5)', [accesscode, layer, zoom, x, y])
           .then(() => {
               resolve({ error: false });
           })
           .catch(error => {
               console.log(error);
               reject({ error: true });
           });
   });
};

const checkEagleViewPurchase = (accesscode, layer) => {
   return new Promise(function (resolve, reject) {
       pdb.any('SELECT * FROM eagleview_access_log WHERE accesscode = $1 AND layer = $2', [accesscode, layer])
           .then(function (data) {
               resolve(data.length > 0);
           })
           .catch(function (error) {
               reject(error);
           });
   });
};

const logEagleViewPurchase = (accesscode, layer, bounds, tileMatrixSet) => {
    const geojson = {
        type: 'Polygon',
        coordinates: [bounds],
    };

    return new Promise(function (resolve, reject) {
        pdb.one('INSERT INTO eagleview_purchases(accesscode, layer, bounds, tile_matrix_set) VALUES($1, $2, ST_SetSRID(ST_GeomFromGeoJSON($3), 4326), $4) RETURNING id', [accesscode, layer, geojson, tileMatrixSet])
            .then(data => {
                resolve({
                    error: false,
                    id: data.id
                });
            })
            .catch(error => {
                console.log(error);
                reject({
                    error: true
                });
            });

    });
};

const getEagleViewPurchases = (accesscode, layer) => {
    return new Promise(function (resolve, reject) {
        pdb.any('SELECT ST_AsGeoJSON(bounds) as bounds, tile_matrix_set FROM eagleview_purchases WHERE accesscode = $1 AND layer = $2', [accesscode, layer])
            .then(function (data) {
                if (data.length === 0) {
                    resolve({ bounds: [], tileMatrixSet: null });
                    return;
                }
                const bounds = data.map(p => JSON.parse(p.bounds).coordinates[0]);
                const tileMatrixSet = data[0].tile_matrix_set;
                resolve({ bounds, tileMatrixSet });
            })
            .catch(function (error) {
                reject(error);
            });
    });
};


module.exports = {
  logEagleViewAccess,
  checkEagleViewPurchase,
  logNearMap,
  isCustomer,
   logEagleViewPurchase,
  getQuota,
  addQuota,
  userStatus,
  cachedStatus,
  checkCachePic,
  checkCacheNear,
    getEagleViewPurchases,
  addTile,
  allCached,
  logAccess,
  updatePath,
  getAllTiles,
  addTileAccess,
  getTile,
  logAccessClick,
  copyTile,
  urlCached,
  addTile2,
  addbypass,
  checkBypass
}