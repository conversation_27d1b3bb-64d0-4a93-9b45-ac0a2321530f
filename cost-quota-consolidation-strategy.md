# Cost and Quota Management System Consolidation Strategy

**Project:** EagleView Integration & Cost System Unification  
**Date:** July 31, 2025  
**Status:** Analysis Complete - Ready for Implementation  

## Executive Summary

This document outlines the comprehensive strategy to consolidate and unify the cost and quota management system for the imagery platform. The current system has redundant tables, inconsistent cost logic, and missing free access features that need to be addressed for the EagleView transition.

### Critical Issues Identified
1. **EagleView Quota Bypass:** EagleView purchases are not charged against user quotas
2. **Table Redundancy:** 6+ tables handling cost/quota logic that can be consolidated to 3
3. **Missing Free Access:** No implementation of free access for previously purchased bounds
4. **Inconsistent Worker Bypass:** Different bypass mechanisms across providers

### Key Benefits of Consolidation
- **50% reduction** in database complexity
- **Unified cost calculation** across all providers
- **Proper quota enforcement** for EagleView
- **Free access implementation** for previous purchases
- **Consistent worker bypass** across all providers

---

## Current System Analysis

### Database Schema Analysis

#### Core Tables (Keep & Enhance)
**`users` table:**
```sql
-- ACTUAL structure from database schema
CREATE TABLE users (
    users_id INTEGER NOT NULL DEFAULT nextval('users_id_seq'::regclass),
    users_searches SMALLINT NOT NULL,           -- Allocated quota amount
    users_ip VARCHAR(45) NOT NULL,              -- IP address
    users_timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    users_accesscode VARCHAR(255) NOT NULL,     -- Primary identifier for users
    users_forcedownload SMALLINT                -- Force download flag (bypass mechanism)
);
```

**`logs` table:**
```sql
-- ACTUAL structure from database schema
CREATE TABLE logs (
    logs_id INTEGER NOT NULL DEFAULT nextval('logs_id_seq'::regclass),
    logs_timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    logs_tiles_id BIGINT NOT NULL,              -- Reference to tiles
    logs_access_type SMALLINT NOT NULL,         -- Access type (0=self-cached[FREE], 1=new[CHARGED], 2=shared-cached[CHARGED], 3=click[FREE])
    logs_accesscode VARCHAR(255) NOT NULL,      -- User identifier
    logs_bid INTEGER,                           -- Building ID
    logs_cost DOUBLE PRECISION,                 -- Cost charged for this access
    logs_mapid INTEGER                          -- Map ID
);
```

**`tiles` table:**
```sql
-- ACTUAL structure from database schema
CREATE TABLE tiles (
    tiles_id BIGINT NOT NULL DEFAULT nextval('tiles_tiles_id_seq'::regclass),
    tiles_bound GEOMETRY NOT NULL,              -- PostGIS geometry for bounds
    tiles_hit_bound GEOMETRY NOT NULL,          -- Expanded bounds for hit detection
    tiles_accesscode TEXT NOT NULL,             -- Comma-separated list of access codes
    tiles_path VARCHAR(255),                    -- Storage path for tile
    tiles_imagery_date DATE,                    -- Date of imagery
    tiles_bounds_text TEXT,                     -- Bounds as text
    tiles_provider VARCHAR(20),                 -- Provider name (Nearmap, pictometry, etc.)
    tiles_cost REAL                             -- Cost of this tile
);
```

#### EagleView-Specific Tables (To Be Consolidated)
**`eagleview_purchases` table:**
```sql
-- ACTUAL structure from database schema
CREATE TABLE eagleview_purchases (
    id INTEGER NOT NULL DEFAULT nextval('eagleview_purchases_id_seq'::regclass),
    accesscode VARCHAR(255) NOT NULL,           -- User identifier
    layer VARCHAR(255) NOT NULL,                -- EagleView layer identifier
    bounds GEOMETRY NOT NULL,                   -- PostGIS geometry for purchased bounds
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    tile_matrix_set VARCHAR(255)                -- Tile matrix set identifier
);
```

**`eagleview_access_log` table:**
```sql
-- ACTUAL structure from database schema
CREATE TABLE eagleview_access_log (
    id INTEGER NOT NULL DEFAULT nextval('eagleview_access_log_id_seq'::regclass),
    accesscode VARCHAR(255) NOT NULL,           -- User identifier
    layer VARCHAR(255) NOT NULL,                -- EagleView layer identifier
    zoom INTEGER NOT NULL,                      -- Zoom level
    x INTEGER NOT NULL,                         -- Tile X coordinate
    y INTEGER NOT NULL,                         -- Tile Y coordinate
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### Nearmap-Specific Tables (To Be Consolidated)
**`nearmap_accesscode_bypass` table:**
```sql
-- ACTUAL structure from database schema
CREATE TABLE nearmap_accesscode_bypass (
    id INTEGER NOT NULL DEFAULT nextval('nearmap_accesscode_bypass_id_seq'::regclass),
    accesscode VARCHAR(255)                     -- User identifier for bypass
);
```

**`nearmap_image_download_log` table:**
```sql
-- ACTUAL structure from database schema (replaces nearmap_logs)
CREATE TABLE nearmap_image_download_log (
    id INTEGER NOT NULL DEFAULT nextval('nearmap_image_download_log_id_seq'::regclass),
    jobid UUID,
    zoom INTEGER,
    date DATE,
    accesscode VARCHAR(255),
    worker BOOLEAN,
    bid INTEGER,
    cost DOUBLE PRECISION,
    mid INTEGER,
    download_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    uncompressed_size BIGINT,
    url TEXT,
    area DOUBLE PRECISION
);
```

#### Additional Tables Found
**`imagery_transactions` table:** (Already exists!)
```sql
-- ACTUAL structure from database schema
CREATE TABLE imagery_transactions (
    id INTEGER NOT NULL DEFAULT nextval('imagery_transactions_id_seq'::regclass),
    accesscode VARCHAR(255) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    cost NUMERIC(10,2) NOT NULL,
    bounds GEOMETRY,
    tile_matrix_set VARCHAR(255),
    layer_id VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

**`tiles_new` table:** (Appears to be a migration table)
```sql
-- ACTUAL structure from database schema
CREATE TABLE tiles_new (
    tiles_id BIGINT,
    tiles_bound GEOMETRY,
    tiles_hit_bound GEOMETRY,
    tiles_accesscode TEXT,
    tiles_path VARCHAR(512),
    tiles_imagery_date DATE,
    tiles_bounds_text TEXT,
    tiles_provider VARCHAR(20),
    tiles_cost REAL
);
```

### Cost Calculation Logic Analysis

#### Current Pricing Formula (Both Providers)
Both Nearmap and EagleView use identical pricing:

```javascript
// Constants used in both nearmap.js and eagleview.js
const PRICING_FACTOR = 15000;
const SQM_TO_ACRES = 0.000247105;

// Pricing calculation
const boundsArea = turf.area(boundsPoly);
const areaInAcres = boundsArea * SQM_TO_ACRES;
let dollarEstimate = Math.ceil((areaInAcres * 0.25) * 2) / 2;
```

#### Quota Checking Logic
```javascript
// From db.js - getQuota function
const getQuota = (accessCode) => {
  const pr1 = pdb.any("SELECT SUM(users_searches) as allocation FROM users WHERE users_accesscode = $1::text", [accessCode])
  const pr2 = pdb.any("SELECT SUM(logs_cost) as usage FROM logs WHERE logs_accesscode = $1::text AND (logs_access_type=1 OR logs_access_type=2)", [accessCode])
  // Returns: allocation - usage = remaining quota
}
```

#### Cost Enforcement Points

**Nearmap (tiles.js):**
```javascript
if(isWorker || (remainQuota-cost >= 0)) {
    // Allow access and charge cost
    const result = {
        quota: remainQuota-cost,
        // ... other fields
    };
}
```

**EagleView (tilesrouter.js):**
- **CRITICAL ISSUE:** No cost checking currently implemented!
- EagleView purchases are logged but not charged against quota
- JWT tokens control access to purchased bounds, but no quota enforcement

#### Current Bypass Mechanisms

**Worker Bypass:**
```javascript
// From tiles.js
const isWorker = typeof worker == 'undefined' ? false : worker == 0 ? false : true;
if(isWorker || (remainQuota-cost >= 0)) {
    // Workers bypass cost checks
}
```

**Nearmap Bypass Table:**
```javascript
// From db.js
const checkBypass = async (accessCode) => {
  const exists = await pdb.oneOrNone('SELECT id FROM nearmap_accesscode_bypass WHERE accesscode = $1', [accessCode]);
  return exists ? true : false;
};
```

---

## Unified System Design

### New Database Schema

#### Consolidated Tables

**1. `imagery_purchases` (Replaces: tiles + eagleview_purchases)**
```sql
CREATE TABLE imagery_purchases (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL,
    provider TEXT NOT NULL,                    -- 'nearmap', 'eagleview', 'pictometry'
    layer_id TEXT,                            -- EagleView layer ID, NULL for others
    bounds GEOMETRY(POLYGON, 4326) NOT NULL,
    tile_matrix_set TEXT,                     -- EagleView specific
    imagery_date DATE,
    purchase_date TIMESTAMP DEFAULT NOW(),
    cost NUMERIC(10,2) NOT NULL,
    storage_url TEXT,                         -- For cached tiles
    status TEXT DEFAULT 'active',             -- 'active', 'expired', 'cached'
    
    -- Indexes for performance
    INDEX idx_imagery_purchases_accesscode (accesscode),
    INDEX idx_imagery_purchases_provider (provider),
    INDEX idx_imagery_purchases_bounds USING GIST (bounds),
    INDEX idx_imagery_purchases_status (status)
);
```

**2. `usage_logs` (Replaces: logs + eagleview_access_log + nearmap_logs)**
```sql
CREATE TABLE usage_logs (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL,
    provider TEXT NOT NULL,
    purchase_id INTEGER REFERENCES imagery_purchases(id),
    access_type TEXT NOT NULL,                -- 'new_purchase', 'cached_access', 'free_reuse'
    cost_charged NUMERIC(10,2) DEFAULT 0,
    building_id INTEGER,
    map_id INTEGER,
    tile_coordinates JSONB,                   -- {z, x, y} for tile access
    access_date TIMESTAMP DEFAULT NOW(),
    
    -- Additional fields for detailed logging
    uncompressed_size INTEGER,                -- For Nearmap compatibility
    area_acres NUMERIC(10,4),                -- Area in acres
    
    -- Indexes for performance
    INDEX idx_usage_logs_accesscode (accesscode),
    INDEX idx_usage_logs_provider (provider),
    INDEX idx_usage_logs_access_date (access_date),
    INDEX idx_usage_logs_purchase_id (purchase_id)
);
```

**3. `access_bypasses` (Replaces: nearmap_accesscode_bypass + user.forcedownload)**
```sql
CREATE TABLE access_bypasses (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL UNIQUE,
    bypass_type TEXT NOT NULL,                -- 'worker', 'admin', 'free_tier'
    created_date TIMESTAMP DEFAULT NOW(),
    expires_date TIMESTAMP,                   -- NULL for permanent bypass
    
    -- Indexes
    INDEX idx_access_bypasses_accesscode (accesscode),
    INDEX idx_access_bypasses_type (bypass_type)
);
```

---

## Implementation Plan

### Phase 1: Database Migration (Day 1)

#### Step 1.1: Create New Tables
```sql
-- File: migrations/001_create_unified_tables.sql

-- Create imagery_purchases table
CREATE TABLE imagery_purchases (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL,
    provider TEXT NOT NULL,
    layer_id TEXT,
    bounds GEOMETRY(POLYGON, 4326) NOT NULL,
    tile_matrix_set TEXT,
    imagery_date DATE,
    purchase_date TIMESTAMP DEFAULT NOW(),
    cost NUMERIC(10,2) NOT NULL,
    storage_url TEXT,
    status TEXT DEFAULT 'active'
);

-- Create usage_logs table
CREATE TABLE usage_logs (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL,
    provider TEXT NOT NULL,
    purchase_id INTEGER REFERENCES imagery_purchases(id),
    access_type TEXT NOT NULL,
    cost_charged NUMERIC(10,2) DEFAULT 0,
    building_id INTEGER,
    map_id INTEGER,
    tile_coordinates JSONB,
    access_date TIMESTAMP DEFAULT NOW(),
    uncompressed_size INTEGER,
    area_acres NUMERIC(10,4)
);

-- Create access_bypasses table
CREATE TABLE access_bypasses (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL UNIQUE,
    bypass_type TEXT NOT NULL,
    created_date TIMESTAMP DEFAULT NOW(),
    expires_date TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_imagery_purchases_accesscode ON imagery_purchases(accesscode);
CREATE INDEX idx_imagery_purchases_provider ON imagery_purchases(provider);
CREATE INDEX idx_imagery_purchases_bounds ON imagery_purchases USING GIST(bounds);
CREATE INDEX idx_imagery_purchases_status ON imagery_purchases(status);

CREATE INDEX idx_usage_logs_accesscode ON usage_logs(accesscode);
CREATE INDEX idx_usage_logs_provider ON usage_logs(provider);
CREATE INDEX idx_usage_logs_access_date ON usage_logs(access_date);
CREATE INDEX idx_usage_logs_purchase_id ON usage_logs(purchase_id);

CREATE INDEX idx_access_bypasses_accesscode ON access_bypasses(accesscode);
CREATE INDEX idx_access_bypasses_type ON access_bypasses(bypass_type);
```

#### Step 1.2: Migrate Existing Data
```sql
-- File: migrations/002_migrate_existing_data.sql

-- Migrate tiles table data to imagery_purchases
INSERT INTO imagery_purchases (
    accesscode, provider, bounds, imagery_date, cost, storage_url, status
)
SELECT
    split_part(tiles_accesscode, ',', 1) as accesscode,
    tiles_provider as provider,
    tiles_bound as bounds,
    tiles_imagery_date as imagery_date,
    tiles_cost as cost,
    tiles_url as storage_url,
    'cached' as status
FROM tiles
WHERE tiles_provider IS NOT NULL;

-- Migrate eagleview_purchases data to imagery_purchases
INSERT INTO imagery_purchases (
    accesscode, provider, layer_id, bounds, tile_matrix_set, cost, status
)
SELECT
    accesscode,
    'eagleview' as provider,
    layer as layer_id,
    bounds,
    tile_matrix_set,
    0 as cost, -- EagleView purchases weren't charged yet
    'active' as status
FROM eagleview_purchases;

-- Migrate logs data to usage_logs
INSERT INTO usage_logs (
    accesscode, provider, purchase_id, access_type, cost_charged,
    building_id, map_id, access_date
)
SELECT
    l.logs_accesscode,
    COALESCE(t.tiles_provider, 'unknown') as provider,
    ip.id as purchase_id,
    CASE l.logs_access_type
        WHEN 0 THEN 'self_cached'      -- User's own cached tile (FREE)
        WHEN 1 THEN 'new_purchase'     -- New tile purchase (CHARGED)
        WHEN 2 THEN 'shared_cached'    -- Shared cached tile (CHARGED)
        WHEN 3 THEN 'click_access'     -- Click access (FREE)
        ELSE 'unknown'
    END as access_type,
    l.logs_cost,
    l.logs_bid,
    l.logs_mapid,
    NOW() as access_date -- logs table doesn't have timestamp
FROM logs l
LEFT JOIN tiles t ON l.logs_tiles_id = t.tiles_id
LEFT JOIN imagery_purchases ip ON ip.storage_url = t.tiles_url AND ip.accesscode = l.logs_accesscode;

-- Migrate bypass data to access_bypasses
INSERT INTO access_bypasses (accesscode, bypass_type)
SELECT accesscode, 'worker' as bypass_type
FROM nearmap_accesscode_bypass;

-- Migrate users.forcedownload to access_bypasses
INSERT INTO access_bypasses (accesscode, bypass_type)
SELECT users_accesscode, 'admin' as bypass_type
FROM users
WHERE users_forcedownload = 1
ON CONFLICT (accesscode) DO NOTHING;

-- Migrate nearmap_image_download_log to usage_logs
INSERT INTO usage_logs (
    accesscode, provider, access_type, cost_charged,
    building_id, map_id, access_date, uncompressed_size, area_acres
)
SELECT
    accesscode,
    'nearmap' as provider,
    'new_purchase' as access_type,
    cost,
    bid,
    mid,
    download_time,
    uncompressed_size,
    area * 0.000247105 as area_acres  -- Convert sqm to acres
FROM nearmap_image_download_log;
```

#### Step 1.3: Verify Data Migration
```sql
-- File: migrations/003_verify_migration.sql

-- Check row counts match
SELECT 'tiles' as source_table, COUNT(*) as row_count FROM tiles
UNION ALL
SELECT 'eagleview_purchases', COUNT(*) FROM eagleview_purchases
UNION ALL
SELECT 'logs', COUNT(*) FROM logs
UNION ALL
SELECT 'nearmap_accesscode_bypass', COUNT(*) FROM nearmap_accesscode_bypass
UNION ALL
SELECT 'nearmap_image_download_log', COUNT(*) FROM nearmap_image_download_log
UNION ALL
SELECT 'imagery_purchases', COUNT(*) FROM imagery_purchases
UNION ALL
SELECT 'usage_logs', COUNT(*) FROM usage_logs
UNION ALL
SELECT 'access_bypasses', COUNT(*) FROM access_bypasses;

-- Verify cost totals match
SELECT
    'Original logs cost total' as description,
    SUM(logs_cost) as total_cost
FROM logs
WHERE logs_access_type IN (1, 2)
UNION ALL
SELECT
    'New usage_logs cost total',
    SUM(cost_charged)
FROM usage_logs
WHERE access_type IN ('new_purchase', 'shared_cached');
```

### Phase 2: Implement Cost Manager (Day 2)

#### Step 2.1: Create Cost Manager Module
```javascript
// File: cost-manager.js
const turf = require('@turf/turf');
const db = require('./db');

class CostManager {
    constructor() {
        this.PRICING_FACTOR = 15000;
        this.SQM_TO_ACRES = 0.000247105;
    }

    async calculateCost(accessCode, bounds, provider, options = {}) {
        try {
            // 1. Check worker bypass
            if (options.isWorker || await this.checkBypass(accessCode)) {
                return { cost: 0, reason: 'worker_bypass', canProceed: true };
            }

            // 2. Check previous purchases (free access rule)
            const previousPurchase = await this.findOverlappingPurchase(accessCode, bounds, provider);
            if (previousPurchase) {
                return { cost: 0, reason: 'previous_purchase', purchaseId: previousPurchase.id, canProceed: true };
            }

            // 3. Calculate area-based cost
            const areaCost = this.calculateAreaCost(bounds);

            // 4. Check quota availability
            const quota = await this.getQuota(accessCode);
            const canProceed = quota.remaining >= areaCost;

            return {
                cost: areaCost,
                reason: 'new_purchase',
                canProceed: canProceed,
                remainingQuota: quota.remaining,
                requiredQuota: areaCost
            };

        } catch (error) {
            console.error('Error calculating cost:', error);
            throw error;
        }
    }

    calculateAreaCost(bounds) {
        const boundsArea = turf.area(bounds);
        const areaInAcres = boundsArea * this.SQM_TO_ACRES;
        let dollarEstimate = Math.ceil((areaInAcres * 0.25) * 2) / 2;
        return dollarEstimate < 1 ? 1 : dollarEstimate; // Minimum $1
    }

    async processPurchase(accessCode, bounds, provider, options = {}) {
        try {
            const costResult = await this.calculateCost(accessCode, bounds, provider, options);

            if (!costResult.canProceed) {
                return {
                    success: false,
                    error: 'insufficient_quota',
                    required: costResult.requiredQuota,
                    available: costResult.remainingQuota
                };
            }

            const purchaseId = await this.recordPurchase(accessCode, bounds, provider, costResult.cost, options);
            await this.logUsage(accessCode, provider, purchaseId, costResult.cost, 'new_purchase', options);

            return {
                success: true,
                purchaseId: purchaseId,
                cost: costResult.cost,
                reason: costResult.reason,
                remainingQuota: costResult.remainingQuota - costResult.cost
            };

        } catch (error) {
            console.error('Error processing purchase:', error);
            throw error;
        }
    }
}

module.exports = CostManager;
```

### Phase 3: Fix EagleView Integration (Day 3)

#### Step 3.1: Update EagleView Purchase Endpoint
```javascript
// File: tilesrouter.js - Update /eagleview/purchase endpoint

router.post('/eagleview/purchase', async (req, res) => {
    try {
        const { accesscode, layer, bounds, tileMatrixSet } = req.body;

        if (!accesscode || !layer || !bounds || !tileMatrixSet) {
            return res.status(400).json({
                error: 'Missing required parameters',
                required: ['accesscode', 'layer', 'bounds', 'tileMatrixSet']
            });
        }

        // Create bounds polygon for cost calculation
        const boundsPolygon = { type: 'Polygon', coordinates: [bounds] };

        // Use unified cost manager to check quota and process purchase
        const purchaseResult = await db.checkQuotaAndPurchase(
            accesscode, boundsPolygon, 'eagleview',
            { layerId: layer, tileMatrixSet: tileMatrixSet }
        );

        if (!purchaseResult.success) {
            return res.status(402).json({
                error: 'Insufficient quota',
                required: purchaseResult.required,
                available: purchaseResult.available
            });
        }

        // Generate JWT token for the purchased layer
        const { bounds: allPurchasedBounds } = await db.getEagleViewPurchases(accesscode, layer);
        const token = jwt.sign({ accesscode, layer, bounds: allPurchasedBounds, tileMatrixSet },
                              process.env.JWT_SECRET, { expiresIn: '24h' });

        res.status(201).json({
            purchaseId: purchaseResult.purchaseId,
            token: token,
            cost: purchaseResult.cost,
            reason: purchaseResult.reason,
            remainingQuota: purchaseResult.remainingQuota
        });

    } catch (error) {
        console.error('Error processing EagleView purchase:', error);
        res.status(500).json({ error: 'Error processing purchase' });
    }
});
```

### Phase 4: Update Nearmap Integration (Day 4)

#### Step 4.1: Update Nearmap Tile Request
```javascript
// File: tiles.js - Update getNearMapTile function

const getNearMapTile = async (accessCode, bounds, date, worker, bid, mid) => {
    try {
        const turfBounds = geofunctions.convertBoundsStringToTurfBounds(bounds);
        const boundsPoly = turf.bboxPolygon(turfBounds);

        // Check cache first
        const cached = await checkNearMapCache(bounds, date);
        if (cached.length > 0) {
            // Free access to cached tiles
            await db.costManager.logUsage(accessCode, 'nearmap', cached[0].tiles_id, 0, 'cached_access');
            return {
                error: false, url: cached[0].tiles_url, quota: (await db.getUnifiedQuota(accessCode)).remaining,
                provider: "Nearmap", bounds: boundsPoly.geometry, date: date, cost: 0, reason: 'cached_access'
            };
        }

        // Use unified cost manager for new purchases
        const purchaseResult = await db.checkQuotaAndPurchase(accessCode, boundsPoly, 'nearmap',
            { isWorker: worker, imageryDate: date, buildingId: bid, mapId: mid });

        if (!purchaseResult.success) {
            return { error: true, reason: 'insufficient_quota', required: purchaseResult.required, available: purchaseResult.available };
        }

        // Download and process tile
        const data = { bounds: turfBounds, date: date, accessCode: accessCode, worker: worker, bid: bid, cost: purchaseResult.cost, mid: mid };
        const files = await nearmap.downloadTile(data);
        const uploadResults = await Promise.all([storage.uploadFile(files.ofile), storage.uploadFile(files.cfile)]);

        // Update purchase record with storage URL
        await db.query('UPDATE imagery_purchases SET storage_url = $1 WHERE id = $2', [uploadResults[1], purchaseResult.purchaseId]);

        return {
            error: false, url: uploadResults[1], quota: purchaseResult.remainingQuota, provider: "Nearmap",
            bounds: boundsPoly.geometry, date: date, cost: purchaseResult.cost, reason: purchaseResult.reason
        };

    } catch (error) {
        console.error('Error getting Nearmap tile:', error);
        throw error;
    }
};
```

---

## Task List & Timeline

### Immediate Tasks (Priority Order)

1. **[URGENT] Fix EagleView Quota Bypass** (4 hours)
   - Add quota checking to /eagleview/purchase endpoint
   - Prevent unlimited free purchases
   - Status: Ready to implement

2. **Create Unified Cost Manager Module** (1 day)
   - Implement CostManager class
   - Add unified cost calculation logic
   - Status: Design complete

3. **Create Database Migration Scripts** (1 day)
   - Write SQL scripts for new tables
   - Create data migration scripts
   - Status: Design complete

4. **Implement Free Access for Previous Purchases** (1 day)
   - Add geospatial overlap checking
   - Implement across both providers
   - Status: Design complete

5. **Update EagleView Purchase Endpoint** (1 day)
   - Integrate with CostManager
   - Add proper quota enforcement
   - Status: Design complete

6. **Test and Validate New System** (2-3 days)
   - Unit tests for CostManager
   - Integration tests for purchase flows
   - Validation of cost calculations
   - Status: Test plan ready

### Timeline Summary
- **Week 1:** Critical fix + Core implementation (Days 1-4)
- **Week 2:** Testing + Deployment (Days 5-7)
- **Total Estimated Time:** 7-8 days

### Success Metrics
- [ ] EagleView purchases properly charged against quotas
- [ ] Free access working for previously purchased bounds
- [ ] Worker bypass functioning across all providers
- [ ] 50% reduction in database table complexity
- [ ] All existing functionality preserved
- [ ] Zero data loss during migration

### Phase 6: Deployment & Cleanup (Day 7)

#### Step 6.1: Deployment Checklist
- [ ] Database backup completed
- [ ] New tables created successfully
- [ ] Data migration completed and verified
- [ ] All tests passing
- [ ] Cost calculations verified against existing data
- [ ] EagleView quota enforcement working
- [ ] Free access rules implemented
- [ ] Worker bypass functioning
- [ ] Legacy Nearmap access preserved

#### Step 6.2: Table Cleanup Strategy

**IMPORTANT:** Only run cleanup after 100% verification that new system works correctly!

```sql
-- File: migrations/004_cleanup_old_tables.sql
-- Run only after successful deployment and validation

-- PHASE 1: Remove redundant tables (SAFE - data migrated)
DROP TABLE IF EXISTS eagleview_access_log;        -- Migrated to usage_logs
DROP TABLE IF EXISTS eagleview_purchases;         -- Migrated to imagery_purchases
DROP TABLE IF EXISTS nearmap_accesscode_bypass;   -- Migrated to access_bypasses
DROP TABLE IF EXISTS nearmap_image_download_log;  -- Migrated to usage_logs

-- PHASE 2: Clean up migration artifacts
DROP TABLE IF EXISTS tiles_new;                   -- Appears to be old migration table

-- PHASE 3: Update users table (CAREFUL - test first!)
-- Remove forcedownload column since it's now in access_bypasses
ALTER TABLE users DROP COLUMN IF EXISTS users_forcedownload;

-- PHASE 4: Archive old logs (OPTIONAL - keep for historical data)
-- Consider creating archive table instead of dropping
-- CREATE TABLE logs_archive AS SELECT * FROM logs;
-- Keep logs table for now - can archive later after extended validation period

-- PHASE 5: Update schema to use 'postgis' schema (FUTURE)
-- Move all tables to postgis schema as mentioned
-- ALTER TABLE imagery_purchases SET SCHEMA postgis;
-- ALTER TABLE usage_logs SET SCHEMA postgis;
-- ALTER TABLE access_bypasses SET SCHEMA postgis;
```

#### Step 6.3: Rollback Plan (Emergency)
```sql
-- File: migrations/rollback_emergency.sql
-- Emergency rollback if new system fails

-- Step 1: Restore old tables from backup
-- (Assumes you have pg_dump backup from before migration)

-- Step 2: Drop new tables
DROP TABLE IF EXISTS imagery_purchases;
DROP TABLE IF EXISTS usage_logs;
DROP TABLE IF EXISTS access_bypasses;

-- Step 3: Restore application code to previous version
-- (Use git to revert code changes)

-- Step 4: Verify old system functionality
-- (Run existing tests)
```

#### Step 6.4: Post-Cleanup Verification
```sql
-- File: migrations/005_post_cleanup_verification.sql

-- Verify only expected tables remain
SELECT table_name
FROM information_schema.tables
WHERE table_schema IN ('public', 'postgis')
AND table_name NOT LIKE 'spatial_%'
AND table_name NOT LIKE 'geography_%'
AND table_name NOT LIKE 'geometry_%'
AND table_name NOT LIKE 'raster_%'
ORDER BY table_name;

-- Expected remaining tables:
-- - users (updated)
-- - tiles (legacy cached access)
-- - logs (historical data)
-- - imagery_purchases (new unified)
-- - usage_logs (new unified)
-- - access_bypasses (new unified)
-- - imagery_transactions (existing, may need integration)

-- Verify new system quota calculations
SELECT
    accesscode,
    SUM(CASE WHEN access_type IN ('new_purchase', 'shared_cached') THEN cost_charged ELSE 0 END) as new_system_usage
FROM usage_logs
GROUP BY accesscode
LIMIT 10;
```

---

## Risk Assessment & Mitigation

### High Risk Items
1. **Data Loss During Migration**
   - Mitigation: Full database backup before any changes
   - Rollback plan: Keep old tables until validation complete

2. **Cost Calculation Errors**
   - Mitigation: Extensive testing against existing data
   - Validation: Compare new calculations with historical data

3. **Breaking Existing API Contracts**
   - Mitigation: Maintain backward compatibility
   - Testing: Comprehensive integration tests

### Medium Risk Items
1. **Performance Impact of New Queries**
   - Mitigation: Proper indexing on new tables
   - Monitoring: Query performance testing

2. **JWT Token Compatibility**
   - Mitigation: Maintain existing token structure
   - Testing: Token validation tests

---

## Conclusion

This consolidation strategy addresses all identified issues while providing a clear path forward for the EagleView integration. The unified system will be more maintainable, consistent, and feature-complete while preserving all existing functionality.

The most critical immediate action is fixing the EagleView quota bypass to prevent unlimited free purchases. This can be implemented quickly while the full consolidation is being developed.

**Next Step:** Begin implementation with the urgent EagleView quota fix, then proceed with the full consolidation plan.

---

## IMPORTANT DISCOVERY: `imagery_transactions` Table Already Exists!

The database schema reveals that an `imagery_transactions` table already exists with a structure very similar to our proposed `imagery_purchases` table:

```sql
CREATE TABLE imagery_transactions (
    id INTEGER NOT NULL DEFAULT nextval('imagery_transactions_id_seq'::regclass),
    accesscode VARCHAR(255) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    cost NUMERIC(10,2) NOT NULL,
    bounds GEOMETRY,
    tile_matrix_set VARCHAR(255),
    layer_id VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

**RECOMMENDATION:** Before implementing the new `imagery_purchases` table, we should:

1. **Investigate the `imagery_transactions` table:**
   - Check if it's currently being used
   - Examine its data and relationship to existing systems
   - Determine if it can be enhanced instead of creating a new table

2. **Possible Approaches:**
   - **Option A:** Enhance `imagery_transactions` to serve as our unified purchase table
   - **Option B:** Migrate data from `imagery_transactions` to our new `imagery_purchases` table
   - **Option C:** Keep both tables if they serve different purposes

3. **Investigation Queries:**
```sql
-- Check if imagery_transactions is being used
SELECT COUNT(*) FROM imagery_transactions;

-- Check recent activity
SELECT * FROM imagery_transactions ORDER BY created_at DESC LIMIT 10;

-- Check which providers are in the table
SELECT provider, COUNT(*) FROM imagery_transactions GROUP BY provider;
```

This discovery could significantly simplify our consolidation strategy!

---

## REVISED STRATEGY: Zero-Downtime Migration with /api3

**MUCH SAFER APPROACH:** Since `/api2` is in production, we'll create a parallel `/api3` system with the new unified schema.

### **Benefits of /api3 Approach:**
- ✅ **Zero production downtime** - `/api2` continues unchanged
- ✅ **Safe testing** - Full validation before switching
- ✅ **Easy rollback** - Just switch back to `/api2`
- ✅ **Clean slate** - New schema in `postgis` from the start
- ✅ **Gradual migration** - Move users incrementally

### **New Implementation Strategy:**

#### **Phase 1: Create /api3 Environment (Day 1)**
```bash
# Copy existing codebase
cp -r /api2 /api3

# Update package.json and configs for api3
cd /api3
# Update port numbers, service names, etc.
```

#### **Phase 2: Create New PostgreSQL Schema (Day 1)**
```sql
-- Create new schema for api3
CREATE SCHEMA IF NOT EXISTS postgis;

-- Grant permissions
GRANT ALL ON SCHEMA postgis TO pictometry;
GRANT ALL ON ALL TABLES IN SCHEMA postgis TO pictometry;
GRANT ALL ON ALL SEQUENCES IN SCHEMA postgis TO pictometry;
ALTER DEFAULT PRIVILEGES IN SCHEMA postgis GRANT ALL ON TABLES TO pictometry;
ALTER DEFAULT PRIVILEGES IN SCHEMA postgis GRANT ALL ON SEQUENCES TO pictometry;

-- Create unified tables in postgis schema
CREATE TABLE postgis.users (
    users_id INTEGER NOT NULL DEFAULT nextval('postgis.users_id_seq'::regclass),
    users_searches SMALLINT NOT NULL,
    users_ip VARCHAR(45) NOT NULL,
    users_timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    users_accesscode VARCHAR(255) NOT NULL UNIQUE,
    -- Remove users_forcedownload - handled by access_bypasses
    PRIMARY KEY (users_id)
);

CREATE TABLE postgis.imagery_purchases (
    id SERIAL PRIMARY KEY,
    accesscode VARCHAR(255) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    layer_id VARCHAR(255),
    bounds GEOMETRY(POLYGON, 4326) NOT NULL,
    tile_matrix_set VARCHAR(255),
    imagery_date DATE,
    purchase_date TIMESTAMPTZ DEFAULT NOW(),
    cost NUMERIC(10,2) NOT NULL,
    storage_url TEXT,
    status VARCHAR(20) DEFAULT 'active',

    -- Indexes
    INDEX idx_imagery_purchases_accesscode (accesscode),
    INDEX idx_imagery_purchases_provider (provider),
    INDEX idx_imagery_purchases_bounds USING GIST (bounds),
    INDEX idx_imagery_purchases_status (status)
);

CREATE TABLE postgis.usage_logs (
    id SERIAL PRIMARY KEY,
    accesscode VARCHAR(255) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    purchase_id INTEGER REFERENCES postgis.imagery_purchases(id),
    access_type VARCHAR(20) NOT NULL,
    cost_charged NUMERIC(10,2) DEFAULT 0,
    building_id INTEGER,
    map_id INTEGER,
    tile_coordinates JSONB,
    access_date TIMESTAMPTZ DEFAULT NOW(),
    uncompressed_size BIGINT,
    area_acres NUMERIC(10,4),

    -- Indexes
    INDEX idx_usage_logs_accesscode (accesscode),
    INDEX idx_usage_logs_provider (provider),
    INDEX idx_usage_logs_access_date (access_date),
    INDEX idx_usage_logs_purchase_id (purchase_id)
);

CREATE TABLE postgis.access_bypasses (
    id SERIAL PRIMARY KEY,
    accesscode VARCHAR(255) NOT NULL UNIQUE,
    bypass_type VARCHAR(20) NOT NULL,
    created_date TIMESTAMPTZ DEFAULT NOW(),
    expires_date TIMESTAMPTZ,

    -- Indexes
    INDEX idx_access_bypasses_accesscode (accesscode),
    INDEX idx_access_bypasses_type (bypass_type)
);

-- Keep legacy tiles table for cached access (read-only from api2)
CREATE TABLE postgis.legacy_tiles AS
SELECT * FROM public.tiles;
```

#### **Phase 3: Update /api3 Database Configuration (Day 1)**
```javascript
// /api3/db.js - Update to use postgis schema
const cn = {
  user: 'pictometry',
  host: '127.0.0.1',
  database: 'pictometry',
  password: 'Y^<Ta5C+H(d|$,Gj',
  port: 5432,
  schema: 'postgis'  // Force all queries to use postgis schema
}

// Update all queries to use postgis schema explicitly
const query = (sql, params) => {
  // Ensure all table references use postgis schema
  const schemaAwareSql = sql.replace(/\b(users|imagery_purchases|usage_logs|access_bypasses)\b/g, 'postgis.$1');
  return pdb.query(schemaAwareSql, params);
};
```

#### **Phase 4: Migrate Data to New Schema (Day 2)**
```sql
-- Migrate users (excluding forcedownload users)
INSERT INTO postgis.users (users_searches, users_ip, users_timestamp, users_accesscode)
SELECT users_searches, users_ip, users_timestamp, users_accesscode
FROM public.users;

-- Migrate tiles to imagery_purchases
INSERT INTO postgis.imagery_purchases (
    accesscode, provider, bounds, imagery_date, cost, storage_url, status
)
SELECT
    split_part(tiles_accesscode, ',', 1) as accesscode,
    COALESCE(tiles_provider, 'nearmap') as provider,
    tiles_bound as bounds,
    tiles_imagery_date as imagery_date,
    COALESCE(tiles_cost, 0) as cost,
    tiles_path as storage_url,
    'cached' as status
FROM public.tiles
WHERE tiles_provider IS NOT NULL;

-- Migrate eagleview_purchases
INSERT INTO postgis.imagery_purchases (
    accesscode, provider, layer_id, bounds, tile_matrix_set, cost, status, purchase_date
)
SELECT
    accesscode,
    'eagleview' as provider,
    layer as layer_id,
    bounds,
    tile_matrix_set,
    0 as cost,
    'active' as status,
    created_at as purchase_date
FROM public.eagleview_purchases;

-- Migrate logs to usage_logs
INSERT INTO postgis.usage_logs (
    accesscode, provider, purchase_id, access_type, cost_charged,
    building_id, map_id, access_date
)
SELECT
    l.logs_accesscode,
    COALESCE(t.tiles_provider, 'unknown') as provider,
    ip.id as purchase_id,
    CASE l.logs_access_type
        WHEN 0 THEN 'self_cached'
        WHEN 1 THEN 'new_purchase'
        WHEN 2 THEN 'shared_cached'
        WHEN 3 THEN 'click_access'
        ELSE 'unknown'
    END as access_type,
    COALESCE(l.logs_cost, 0) as cost_charged,
    l.logs_bid,
    l.logs_mapid,
    l.logs_timestamp as access_date
FROM public.logs l
LEFT JOIN public.tiles t ON l.logs_tiles_id = t.tiles_id
LEFT JOIN postgis.imagery_purchases ip ON ip.storage_url = t.tiles_path;

-- Migrate bypass data
INSERT INTO postgis.access_bypasses (accesscode, bypass_type)
SELECT accesscode, 'worker' as bypass_type
FROM public.nearmap_accesscode_bypass;

-- Migrate users with forcedownload
INSERT INTO postgis.access_bypasses (accesscode, bypass_type)
SELECT users_accesscode, 'admin' as bypass_type
FROM public.users
WHERE users_forcedownload = 1
ON CONFLICT (accesscode) DO NOTHING;
```

#### **Phase 5: Implement /api3 with New Cost Manager (Days 3-4)**
- Copy all `/api2` code to `/api3`
- Implement the new `CostManager` class
- Update all endpoints to use new schema
- Fix EagleView quota enforcement
- Implement free access rules

#### **Phase 6: Testing & Validation (Days 5-6)**
- Run `/api3` in parallel with `/api2`
- Test all functionality with new system
- Validate cost calculations match
- Performance testing

#### **Phase 7: Gradual Migration (Days 7-14)**
- Start routing new users to `/api3`
- Monitor both systems
- Gradually migrate existing users
- Keep `/api2` as fallback

#### **Phase 8: Full Cutover (After validation)**
- Route all traffic to `/api3`
- Keep `/api2` for emergency rollback
- Eventually decommission `/api2`

### **Deployment Strategy:**
```bash
# Run both APIs simultaneously
pm2 start /api2/app.js --name "api2-production"
pm2 start /api3/app.js --name "api3-staging"

# Use nginx to route traffic
# Initially: 100% to api2
# Testing: Route specific test users to api3
# Migration: Gradually shift traffic to api3
```

This approach is **much safer** and allows for thorough testing without any risk to production!
