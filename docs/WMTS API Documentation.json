{"x-generator": "NSwag v14.0.3.0 (NJsonSchema v11.0.0.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "WMTS API Documentation", "description": "# Introduction\n\n## About\n\nThe WMTS (Web Map Tile Service) API allows you to integrate EagleView ortho imagery into GIS and CAD software or web applications.\n\nRefer to the [WMTS API User Guides](https://developer.eagleview.com/documentation/wmts/v1/guides/getting-started) for help getting started.\n\n## Documentation Overview\n\nEach API endpoint in this documentation consists of:\n- **The HTTP method.** Currently includes `GET`.\n- **The base path.** The base path is `https://sandbox.apis.eagleview.com` for the sandbox or `https://apis.eagleview.com` for production. This base path is specified before the endpoint path.\n- **The endpoint path.** For example, `/imagery/wmts/v1/visual/capabilities.xml`.\n- **Required parameters.** These parameters must be included in a request.\n- **Optional parameters.** These parameters can be included in a request if desired.\n\n## Sandbox\n\nThe WMTS API can be tested in sandbox mode. The sandbox testing area is limited to the following bounding box, which covers approximately 1.5 square miles in Omaha, Nebraska:\n\n```\n-96.00532698173473, 41.24140396772262, -95.97589954958912, 41.25672882015283\n```\n\nRefer to the [WMTS API User Guides](https://developer.eagleview.com/documentation/wmts/v1/guides/getting-started) for a Postman collection which contains a number of sample requests.\n\n# Authentication\n\n<!-- ReDoc-Inject: <security-definitions> -->", "version": "v1"}, "servers": [{"url": "https://apis.eagleview.com"}], "paths": {"/imagery/wmts/v1/visual/tile/{layer}/default/{tile_matrix_set}/{z}/{x}/{y}.{format}": {"get": {"tags": ["GetTile"], "summary": "Retrieve a visual tile", "description": "Returns a 256x256 slippy-map aligned visual tile.", "operationId": "GET /imagery/wmts/v1/visual/tile/*", "parameters": [{"name": "layer", "in": "path", "required": true, "description": "The layer to fetch the tile for, obtained from the GetCapabilities response (e.g. `Latest`, `LatestBestResolution` or `<a valid image urn>`).", "schema": {"type": "string"}, "x-position": 1, "example": "Latest"}, {"name": "tile_matrix_set", "in": "path", "required": true, "description": "The tile matrix set that is associated with the layer, obtained from the GetCapabilities response.", "schema": {"type": "string", "nullable": true}, "x-position": 2, "example": "GoogleMapsCompatible_9-23"}, {"name": "z", "in": "path", "required": true, "description": "The tile zoom level, as per the [Slippy Map Tilenames](https://wiki.openstreetmap.org/wiki/Slippy_map_tilenames) specification.", "schema": {"type": "integer"}, "x-position": 3, "example": 20}, {"name": "x", "in": "path", "required": true, "description": "The tile column, as per the [Slippy Map Tilenames](https://wiki.openstreetmap.org/wiki/Slippy_map_tilenames) specification.", "schema": {"type": "integer"}, "x-position": 4, "example": 286671}, {"name": "y", "in": "path", "required": true, "description": "The tile row, as per the [Slippy Map Tilenames](https://wiki.openstreetmap.org/wiki/Slippy_map_tilenames) specification.", "schema": {"type": "integer"}, "x-position": 5, "example": 437890}, {"name": "format", "in": "path", "required": true, "description": "The image format of the returned tile:\n* `jpeg`: quality can be adjusted via the `quality` query parameter, defaults to lossy 75% quality jpeg without alpha (smaller file size, recommended)\n* `png`: lossless png with alpha (larger file size)", "schema": {"type": "string", "nullable": true}, "x-position": 6, "example": "jpeg"}, {"name": "quality", "in": "query", "description": "Optionally specify the quality of JPEG tiles based on compression/bandwidth requirements. A higher quality will result in a larger file size. Expressed as a percentage between 70 - 90.", "schema": {"type": "integer", "default": 75, "nullable": true}, "x-position": 7}], "responses": {"200": {"description": "A visual image of a 256x256 slippy-map aligned tile."}, "400": {"description": "The request is invalid."}, "401": {"description": "The request is unauthorized, either token is expired or invalid."}, "403": {"description": "The request is forbidden, either token is not provided or user is not entitled to access the requested resource."}, "404": {"description": "The requested resource is not found."}, "429": {"description": "Too many requests in a given amount of time."}, "500": {"description": "An unexpected internal server error occurred."}, "503": {"description": "Upstream service is unavailable."}, "504": {"description": "The request has timed out."}}}}, "/imagery/wmts/v1/visual/capabilities.xml": {"get": {"tags": ["GetCapabilities"], "summary": "Retrieve the WMTS capabilities for orthomosaic imagery", "description": "Returns information about the WMTS service parameters in XML format including a list of orthomosaic layers, constrained to any filters that are optionally supplied.\nThis WMTS capabilities information is based on the [OGC WMTS 1.0.1 specification](https://www.ogc.org/standard/wmts/).", "operationId": "GET /imagery/wmts/v1/visual/capabilities.xml", "parameters": [{"name": "capture_date_greater_than_or_equal_to", "in": "query", "description": "Limit results to orthomosaics captured on or after the specified date, expressed as YYYY-MM-DD.", "schema": {"type": "string", "format": "date", "nullable": true}, "x-position": 1, "example": "2020-01-01"}, {"name": "capture_date_less_than_or_equal_to", "in": "query", "description": "Limit results to orthomosaics captured on or before the specified date, expressed as YYYY-MM-DD.", "schema": {"type": "string", "format": "date", "nullable": true}, "x-position": 2, "example": "2022-12-31"}, {"name": "first_published_time_greater_than_or_equal_to", "in": "query", "description": "Limit results to orthomosaics first published on or after the specified time, expressed as RFC3339.", "schema": {"type": "string", "nullable": true}, "x-position": 3, "example": "2023-01-01T00:00:00Z"}, {"name": "first_published_time_less_than", "in": "query", "description": "Limit results to orthomosaics first published before the specified time, expressed as RFC3339.", "schema": {"type": "string", "nullable": true}, "x-position": 4, "example": "2023-02-01T00:00:00Z"}, {"name": "min_longitude", "in": "query", "description": "Minimum longitude of the bounding box. Limit results to only those that intersect the supplied bounding box (all 4 parameters must be supplied), expressed in WGS84 (EPSG:4326).", "schema": {"type": "number", "format": "double", "nullable": true}, "x-position": 5, "example": -81.5508}, {"name": "min_latitude", "in": "query", "description": "Minimum latitude of the bounding box. Limit results to only those that intersect the supplied bounding box (all 4 parameters must be supplied), expressed in WGS84 (EPSG:4326).", "schema": {"type": "number", "format": "double", "nullable": true}, "x-position": 6, "example": 28.3731}, {"name": "max_longitude", "in": "query", "description": "Maximum longitude of the bounding box. Limit results to only those that intersect the supplied bounding box (all 4 parameters must be supplied), expressed in WGS84 (EPSG:4326).", "schema": {"type": "number", "format": "double", "nullable": true}, "x-position": 7, "example": -81.5477}, {"name": "max_latitude", "in": "query", "description": "Maximum latitude of the bounding box. Limit results to only those that intersect the supplied bounding box (all 4 parameters must be supplied), expressed in WGS84 (EPSG:4326).", "schema": {"type": "number", "format": "double", "nullable": true}, "x-position": 8, "example": 28.3758}, {"name": "format", "in": "query", "style": "form", "explode": true, "description": "Optionally specify one or more image formats in priority order to be returned in the GetCapabilities response.", "schema": {"type": "array", "default": "format=jpeg&format=png", "nullable": true, "items": {"type": "string"}}, "x-position": 9}, {"name": "quality", "in": "query", "description": "Optionally specify the quality of JPEG tiles based on compression/bandwidth requirements. A higher quality will result in a larger file size. Expressed as a percentage between 70 - 90.", "schema": {"type": "integer", "default": 75, "nullable": true}, "x-position": 10}, {"name": "layer_title", "in": "query", "description": "The format of each layer title:\n* `readable`: the layer title for each orthomosaic will consist of the Capture Date and either a Custom Capture Label (if specified) or a human readable location. Custom Capture Labels are specified via the Layer Admin app in EV Cloud. Recommended for end users (i.e. consumers using WMTS via a GIS application).\n* `urn`: the layer title for each orthomosaic will consist of the Capture Date and the Unique Reference Number (URN) of the orthomosaic. Recommended for programmatic access where performance is more important than readability.", "schema": {"type": "string", "default": "readable", "nullable": true}, "x-position": 11}, {"name": "size", "in": "query", "description": "The number of individual orthomosaic layers to return, up to 1000. By default, the \"Latest\" layer, \"LatestBestResolution\" layer and up to 100 individual layers are returned.", "schema": {"type": "integer", "default": 100}, "x-position": 12}], "responses": {"200": {"description": "The response is returned in XML and is compliant with the OGC WMTS 1.0.1 specification. It includes:\n* A \"Latest\" layer - a composited layer of the latest orthomosaic imagery available. The latest orthomosaic imagery is always returned, even if an older orthomosaic with higher resolution is available at the requested zoom level. Use this layer to ensure that orthomosaics remain constant when transitioning between zoom levels.\n* A \"Latest (Best Resolution)\" layer - a composited layer of the latest orthomosaic imagery available for the requested zoom level. Imagery may change when transitioning between zoom levels if higher resolution orthomosaics are available for the requested zoom level.\n* A list of individual orthomosaic layers, ordered by capture date with newest first.\n* The Title for each orthomosaic, formatted depending on the `layer_title` specified in the request.\n* The Identifier is an immutable Unique Reference Number (URN) for each orthomosaic.\n* The Abstract includes the Capture Date (or Capture Date range) and First Published Time for each orthomosaic.\n* The Bounding Box is returned in both Web Mercator (EPSG:3857) and WGS84 (EPSG:4326) for each orthomosaic."}, "400": {"description": "The request is invalid."}, "401": {"description": "The request is unauthorized, either token is expired or invalid."}, "403": {"description": "The request is forbidden, either token is not provided or user is not entitled to access the requested resource."}, "404": {"description": "The requested resource is not found."}, "429": {"description": "Too many requests in a given amount of time."}, "500": {"description": "An unexpected internal server error occurred."}, "503": {"description": "Upstream service is unavailable."}, "504": {"description": "The request has timed out."}}}}}, "components": {"securitySchemes": {"Bearer": {"type": "http", "description": "Bearer token", "scheme": "bearer", "bearerFormat": "JWT"}, "HeaderApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key in header (X-API-KEY)", "name": "X-API-KEY", "in": "header"}, "QueryParamApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key in query string (api_key=\\<token\\>)", "name": "api_key", "in": "query"}}}, "security": [{"Bearer": [], "HeaderApiKey": [], "QueryParamApiKey": []}], "tags": [{"name": "GetCapabilities", "description": "The GetCapabilities request is the starting point for a client. It returns information about the WMTS service parameters. \nBy default, a GetCapabilities request will return a “Latest” basemap layer, “Latest (Best Resolution)” basemap layer, and up to 100 individual orthomosaic layers, ordered by the newest captured first."}, {"name": "GetTile", "description": "Using the service parameters returned by the GetCapabilities request, the client can utilise the GetTile request to retrieve a 256x256px slippy-map aligned tile."}]}