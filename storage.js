const db = require('./db')
const validUrl = require('valid-url')
const path = require('path')
const fs = require('fs')
const mkdirp = require('mkdirp')
const Minio = require('minio')

var minioClient = new Minio.Client({
    endPoint: 's3.amazonaws.com',
    accessKey: '********************',
    secretKey: 'gtkRO/7gXY6KoMOiB/TXC2sz1dM07F902KNAxelS'
});

   

const uploadFile = (filePath) => {
    return new Promise(function (resolve, reject) {
        var myFileName = path.basename(filePath)
        var metaData = {
            'Content-Type': 'image/jpeg'
        }
        minioClient.fPutObject('sitefotos-maptiles', myFileName, filePath, metaData, function(err, etag) {
            if (err) return console.log(err)
            resolve('https://sitefotos-maptiles.s3.amazonaws.com/'+myFileName)
        });  
    })
}

const uploadFileBuffer = (buffer, name, type) => {
    return new Promise(function (resolve, reject) {
        
        let metaData = {
            'Content-Type': 'image/png'
        }
        if(type == '.jpg') {
            metaData = {
                'Content-Type': 'image/jpeg'
            }
        }
        if(type == '.webp') {
            metaData = {
                'Content-Type': 'image/webp'
            }
        }
        minioClient.putObject('sitefotos-maptiles', name, buffer, metaData, function(err, etag) {
            if (err) return console.log(err)
            console.log('https://sitefotos-maptiles.s3.amazonaws.com/'+name)
            resolve('https://sitefotos-maptiles.s3.amazonaws.com/'+name)
        });  
    })
}

const syncStorage = () => {
    return new Promise(function (resolve, reject) {
        resolve(true)
     
        db.getAllTiles().then(values =>{
            var objects = []
            var metaData = {
                'Content-Type': 'image/jpeg'
            }
            var stream = minioClient.listObjects('sitefotos-tiles','',true)
            stream.on('data', function(obj) { objects.push(obj) } )
            stream.on('error', function(err) { console.log(err) } )
            stream.on('end', function() {
      
    
                values.forEach(value => {
                    if(!validUrl.isUri(value.tiles_url))
                    { 
                        var myPath = '/var/www/'+ value.tiles_path;
                        var myFileName = path.basename(myPath)
                            fs.access(myPath, fs.F_OK, (err) => {
                            if (err) {
                              console.error('does not exist');
                              return;
                            }
                            let obj = objects.find(o => o.name === myFileName);
                            if(typeof obj == 'undefined') {
                                minioClient.fPutObject('sitefotos-tiles', myFileName, myPath, metaData, function(err, etag) {
                                    if (err) return console.log(err)
                                    db.updatePath(value.tiles_id, 'https://sitefotos-tiles.ewr1.vultrobjects.com/'+myFileName )
                                });  
                            
                            
                           
                                var oName = path.basename(myPath,'.jpg') + '-orignal.jpg'
                                var oPath = '/var/www/' + path.dirname(value.tiles_path) + "/" + oName
                           
                                fs.access(oPath, fs.F_OK, (err) => {
                                    if (err) {
                                        console.error('does not exist');
                                        return;
                                    }
                                    let obj = objects.find(o => o.name === oName);
                                    if(typeof obj == 'undefined') {
                                        minioClient.fPutObject('sitefotos-tiles', oName, oPath, metaData, function(err, etag) {
                                            if (err) return console.log(err)
                                        
                                        }); 
                                    }
                                })
                            }
                            else
                            {
                                db.updatePath(value.tiles_id, 'https://sitefotos-tiles.ewr1.vultrobjects.com/'+obj.name )
                            }
    
                          }); 
                       
                    }
                });
            })
            
            
           
        })
    })
}

module.exports = {
   syncStorage,
   uploadFile,
   uploadFileBuffer
}