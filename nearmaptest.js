const express = require('express')
const geofunctions = require('./geofunctions')
const nearmap = require('./nearmap')
const router = express.Router()

// middleware that is specific to this router
router.use(function timeLog(req, res, next) {
    console.log('Time: ', Date.now())
    next()
})

router.get('/', function (req, res) {
    res.send('NearMap Test Page')
})


router.get('/calcimagesize/bounds/:bounds/zoom/:zoom', function (req, res) {
    const p = geofunctions.getBoundsArea(req.params.bounds)
    res.send(p)
})

router.post('/testdownload', function (req, res) {
    console.log(JSON.parse(req.body.bounds))
    data = {
        bounds: JSON.parse(req.body.bounds),
        date: req.body.date
    }
 
    
    nearmap.downloadTile(data).then(function (result) {
       res.send(result)
    }).catch(function (e) {
        console.log("Error")
        res.send(e)
    })
   
})

router.get('/getimagerydetails/bounds/:bounds', function (req, res) {

    nearmap.getImageryDetails(req.params.bounds).then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/getimagery/bounds/:bounds', function (req, res) {
    nearmap.getImagery(req.params.bounds).then(function (result) {
        res.send(result)
    }, function (err) {
        res.send(err)
    })
})

router.get('/stitch', function (req, res) {
    const filenames = [ './tmp/00853e37-96f8-45c8-8632-721ac0239635.jpg',
    './tmp/9547338a-1a83-48dc-a44a-c8c8baced5ec.jpg',
    './tmp/ff3283e0-f98f-4a48-96d0-2ce88d364022.jpg',
    './tmp/e15b749c-49fc-4b7c-bac5-4c6c944d18ad.jpg',
    './tmp/57898101-9745-40d0-a704-429263d6d930.jpg',
    './tmp/4f81d7c4-59d0-4813-876c-43f8860d7433.jpg',
    './tmp/5a1c7be2-b486-47aa-a7f5-bb1d48375585.jpg',
    './tmp/31d9cce8-49bf-4657-9cf8-b84d71867b01.jpg',
    './tmp/8621ec8f-1142-47ee-a04d-666f85d220f2.jpg',
    './tmp/00b01c1b-88f6-4692-921c-ca4499a3065e.jpg',
    './tmp/1574d697-ad71-4d65-9199-4c6c9875e71c.jpg',
    './tmp/540d4b8b-8c18-4525-bdd7-2c54a3a836c4.jpg',
    './tmp/05c6296c-c163-43f3-bd05-c6384831ccab.jpg',
    './tmp/e5244000-7559-4b6b-80e3-977f9479cb80.jpg',
    './tmp/f790cdf9-5da7-42a9-89c5-83d8ba300be7.jpg',
    './tmp/8b87b755-f2ca-4b01-9f66-f34e0a68cb33.jpg' ]
    const partition = 4
    nearmap.stitch(filenames, partition).then(function (result) {
        res.send(result)
    }, function (err) {
        res.send(err)
    })
})

module.exports = router