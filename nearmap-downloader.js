const geofunctions = require('./geofunctions')
const { URLSearchParams } = require('url');
/*const { HttpsProxyAgent } = require('https-proxy-agent'); */
const db = require('./db')
const turf = require('@turf/turf');
const LatLon = require('geodesy').LatLonEllipsoidal;
const fetch = require('node-fetch');
const fs = require('fs');
const { execFile } = require('child_process');
const { promisify } = require('util');
const execFileAsync = promisify(execFile);
const Papa = require('papaparse');
const proj4 = require('proj4');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const sharp = require('sharp');
const storage = require('./storage')
const lockfile = require('proper-lockfile');
const MAX_IMAGE_PIXELS = 64000000;
const MAX_IMAGE_DIMENSION = 8000;
const JPEG_QUALITY = 80;
const PRICING_FACTOR = 15000;

const FACTOR = 0.0574823;
const firstProjection = 'EPSG:4326'; 
const secondProjection = `PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984_Major_Auxiliary_Sphere",DATUM["D_WGS_1984_Major_Auxiliary_Sphere",SPHEROID["WGS_1984_Major_Auxiliary_Sphere",6378137.0,0.0]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.017453292519943295]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0]]`;

let TOKEN = null;
const TMP_DIRECTORY = './tmp/';




const getToken = async () => {
    const filePath = 'token.json';

    const release = await lockfile.lock(filePath);

    try {
        // Load token from file
        const data = fs.readFileSync(filePath, 'utf8');
        const { refresh_token, refresh_token_expires_in, last_token_refresh_time } = JSON.parse(data);

        const timeLeft = (refresh_token_expires_in * 1000) - (Date.now() - last_token_refresh_time);

        const encodedParams = new URLSearchParams();
        encodedParams.set('client_id', 'oS5KpAjL1eCYLJJI');
        encodedParams.set('redirect_uri', 'https://oauthdebugger.com/debug');
        encodedParams.set('refresh_token', refresh_token);

        // Decide which grant type to use
        let isExchangeRefreshToken = false;
        if (timeLeft <= 345600000) { // Less than or equal to 96 hours
            encodedParams.set('grant_type', 'exchange_refresh_token');
            isExchangeRefreshToken = true;
        } else {
            encodedParams.set('grant_type', 'refresh_token');
        }

        let url = 'https://www.arcgis.com/sharing/rest/oauth2/token/';

        let options = {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: encodedParams
        };

        const response = await fetch(url, options);
        const json = await response.json();
        console.log(json);

        if(isExchangeRefreshToken && json.refresh_token) {
            const updatedData = {
                refresh_token: json.refresh_token,
                refresh_token_expires_in: json.refresh_token_expires_in,
                last_token_refresh_time: Date.now()
            };
            fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 4));
        }

        if(json.access_token) {
            TOKEN = json.access_token;
            return json.access_token;
        }
        
    } catch (err) {
        console.error('error:', err);
    } finally {
        await release();
    }
};


/*
const getToken = async () => {
    const filePath = 'token.json';

    const release = await lockfile.lock(filePath);

    try {
        // Load token from file
        const data = fs.readFileSync(filePath, 'utf8');
        const { refresh_token, refresh_token_expires_in, last_token_refresh_time } = JSON.parse(data);

        const encodedParams = new URLSearchParams();
        encodedParams.set('client_id', 'oS5KpAjL1eCYLJJI');
        encodedParams.set('redirect_uri', 'https://oauthdebugger.com/debug');
        encodedParams.set('refresh_token', refresh_token);
        encodedParams.set('grant_type', 'exchange_refresh_token');

        let url = 'https://www.arcgis.com/sharing/rest/oauth2/token/';

        let options = {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: encodedParams
        };

        const response = await fetch(url, options);
        const json = await response.json();
        console.log(json);

        if(json.refresh_token) {
            const updatedData = {
                refresh_token: json.refresh_token,
                refresh_token_expires_in: json.refresh_token_expires_in,
                last_token_refresh_time: Date.now()
            };
            fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 4));
        }
        if(json.access_token) {
            TOKEN = json.access_token;
            return json.access_token;
        }
        
    } catch (err) {
        console.error('error:' + err);
    } finally {
        await release();
    }
};
*/

function zoom_level(bbox, size, scale_at_zoom_level_0=156543.03){
    const [min_x, min_y, max_x, max_y] = bbox.split(',').map(Number);
    const width_pixels = Number(size.split(',')[0]);

    // Convert from Web Mercator (EPSG:3857) to latitude/longitude (EPSG:4326)
    const [min_lon, min_lat] = mercator_to_latlon(min_x, min_y);
    const [max_lon, max_lat] = mercator_to_latlon(max_x, max_y);

    // Compute the distance using the Vincenty formula
    const p1 = new LatLon(min_lat, min_lon);
    const p2 = new LatLon(max_lat, max_lon);
    const distance_m = p1.distanceTo(p2);

    // Compute the scale in meters/pixel
    const scale = distance_m / width_pixels;

    // Compute the equivalent zoom level
    const zoom_level = Math.log2(scale_at_zoom_level_0 / scale);
    return Math.round(zoom_level);
}

function mercator_to_latlon(x, y){
    const lon = x / 20037508.34 * 180;
    let lat = y / 20037508.34 * 180;
    lat = 180 / Math.PI * (2 * Math.atan(Math.exp(lat * Math.PI / 180)) - Math.PI / 2);
    return [lon, lat];
}

function compute_size2(bbox, zoom_level, scale_at_zoom_level_0=156543.03){
   
    const [min_x, min_y, max_x, max_y] = bbox;
    console.log(min_x, min_y, max_x, max_y)
    // Convert from Web Mercator (EPSG:3857) to latitude/longitude (EPSG:4326)
    const [min_lon, min_lat] = mercator_to_latlon(min_x, min_y);
    const [max_lon, max_lat] = mercator_to_latlon(max_x, max_y);

    // Compute the distance in x and y direction separately using the Vincenty formula
    const p1 = new LatLon(min_lat, min_lon);
    const p2 = new LatLon(max_lat, max_lon);
    const distance_m = p1.distanceTo(p2);

    // Compute the scale in meters/pixel
    const scale = scale_at_zoom_level_0 / Math.pow(2, zoom_level);

    // Compute the size in x and y direction in pixels separately
    const width_pixels = Math.round((max_x - min_x) / scale);
    const height_pixels = Math.round((max_y - min_y) / scale);
    
    return `${width_pixels},${height_pixels}`;
}

function compute_size(bbox, zoom_level, scale_at_zoom_level_0=156543.03){
   
    const [min_lon, min_lat, max_lon, max_lat] = bbox;
    

    // Compute the distance in x and y direction separately using the Vincenty formula
    const p1 = new LatLon(min_lat, min_lon);
    const p2 = new LatLon(max_lat, max_lon);
    const distance_m = p1.distanceTo(p2);

    // Compute the scale in meters/pixel
    const scale = scale_at_zoom_level_0 / Math.pow(2, zoom_level);

    // Here, since our bbox coordinates are already in EPSG:4326, 
    // we need to calculate the distance in meters for width and height separately 
    const width_meters = new LatLon(min_lat, min_lon).distanceTo(new LatLon(min_lat, max_lon));
    const height_meters = new LatLon(min_lat, min_lon).distanceTo(new LatLon(max_lat, min_lon));

    // Then we convert these distances to pixels
    const width_pixels = Math.round(width_meters / scale);
    const height_pixels = Math.round(height_meters / scale);
    
    return `${width_pixels},${height_pixels}`;
}



function convert_to_4326(url_string){
    const url = new URL(url_string);
    const bbox = url.searchParams.get('bbox');
    const bbox_parts = bbox.split(',').map(Number);

    // Convert each part of the bounding box to EPSG:4326
    const [min_x, min_y] = mercator_to_latlon(bbox_parts[0], bbox_parts[1]);
    const [max_x, max_y] = mercator_to_latlon(bbox_parts[2], bbox_parts[3]);

    // Create a new bounding box string with the converted coordinates
    const bbox_4326 = `${min_x},${min_y},${max_x},${max_y}`;

    // Replace the bbox and bboxSR parameters in the URL
    const new_params = new URLSearchParams(url.searchParams);
    new_params.set('bbox', bbox_4326);
    new_params.set('bboxSR', '4326');

    // Construct a new URL with the updated parameters
    const new_url = new URL(url);
    new_url.search = new_params.toString();

    return new_url.href;
}

function boundsToPolygon(bounds) {
    const xmin = bounds[0];
    const ymin = bounds[1];
    const xmax = bounds[2];
    const ymax = bounds[3];
    const lowerLeft = proj4(firstProjection, secondProjection, [xmin, ymin]);
    const upperRight = proj4(firstProjection, secondProjection, [xmax, ymax]);

    return {
        "rings": [[[lowerLeft[0], lowerLeft[1]], [upperRight[0], lowerLeft[1]], [upperRight[0], upperRight[1]], [lowerLeft[0], upperRight[1]], [lowerLeft[0], lowerLeft[1]]]],
        "spatialReference": {"wkid": 102100}
    };
}

async function getImageDate(bounds) {
    const imageserverUrl = "https://aom-us.nearmap.com/arcgis/rest/services/nearmap_us/ImageServer";
    const polygon = boundsToPolygon(bounds); // Call boundsToPolygon function here

    const identifyUrl = `${imageserverUrl}/identify?f=json&geometry=${encodeURIComponent(JSON.stringify(polygon))}&geometryType=esriGeometryPolygon&mosaicRule=${encodeURIComponent('{"method": "esriMosaicAttribute","ascending":false,"sortField":"AcquisitionDate"}')}&token=${TOKEN}`;

    const response = await fetch(identifyUrl);
    const data = await response.json();

  

    return data;
}

async function parseCsv(filePath) {
    const csvFile = await fs.promises.readFile(filePath, 'utf-8');
    const results = Papa.parse(csvFile, {
        header: true,
        dynamicTyping: true,
        skipEmptyLines: true,
        transformHeader: header =>
            header
                .toLowerCase()
                .replace(/\W/g, '_')
    });
    return results.data;
}
async function getParcel(lat, lon) {
    return await (await fetch('https://parcels.sitefotos.com/getgeo?lat=' + lat + '&lon=' +
        lon)).text();
}

function expandBounds(parcelGeoJson, percentageIncrease) {
    // Convert geojson to turf feature
    const parcel = turf.feature(parcelGeoJson);

    // Get the bounding box of the polygon [minLng, minLat, maxLng, maxLat]
    const bbox = turf.bbox(parcel);
    
    // Calculate the horizontal and vertical increase
    const horizontalIncrease = (bbox[2] - bbox[0]) * percentageIncrease;
    const verticalIncrease = (bbox[3] - bbox[1]) * percentageIncrease;

    // Return the expanded bounding box
    return [bbox[0] - horizontalIncrease, bbox[1] - verticalIncrease, bbox[2] + horizontalIncrease, bbox[3] + verticalIncrease];
}
function turf_to_bbox(turfBounds){
    const [min_x, min_y, max_x, max_y] = turfBounds
    const bbox = `${min_x},${min_y},${max_x},${max_y}`;
    return bbox;
}
async function init(filename, latField, lonField)
{
    const data = await parseCsv(filename);
    for(let i = 0; i < data.length; i++)
    {
        try {

            const parcelGeoJson = JSON.parse(await getParcel(data[i][latField], data[i][lonField]));
           
            const expandedBounds = expandBounds(parcelGeoJson, 0.25); // 0.2 for 25% increase
            const boungsPoly = turf.bboxPolygon(expandedBounds);
            const boundsArea = turf.area(boungsPoly);
            const dataEstimate = boundsArea / PRICING_FACTOR;
            let dollarEstimate = Math.ceil((dataEstimate * 0.5) * 2) / 2;
            if(dollarEstimate < 1)
                dollarEstimate = 1;
            
            let files = await downloadTile(expandedBounds);
            let imageryData = await getImageDate(expandedBounds);
           

            let features = imageryData.catalogItems.features;

            let maxAcquisitionDate = Math.max(...features.map(feature => feature.attributes.acquisitiondate));

            let featureWithMaxAcquisitionDate = features.find(feature => feature.attributes.acquisitiondate === maxAcquisitionDate);
            let date = featureWithMaxAcquisitionDate.attributes.nmd
            const values2 = await Promise.all([storage.uploadFile(files.ofile), storage.uploadFile(files.cfile)]);
        
            
            const result3 = await db.addTile2('8f85517967795eeef66c225f7883bdcb', values2[1], date, expandedBounds, boungsPoly.geometry, 'nearmap', dollarEstimate);
           // console.log(featureWithMaxAcquisitionDate.attributes.nmd);
            //return;
            /*const bounds = await getBounds(parcel);
            const date = await getImageDate(bounds);
            console.log(date); */
            console.log(`Sucess Processing Row: ${i}`);
            
            let sucessRow = Object.values(data[i]).join(',') // get the values of data[i] and join them with commas
            //add values2[1] to the end of sucessRow commas
            sucessRow = sucessRow + ',' + values2[1] + '\n';
            fs.promises.appendFile('success.csv', sucessRow);

        } catch(ex) {
            console.log(`Error Processing Row: ${i}`);
            console.log(ex)
            const failedRow = Object.values(data[i]).join(',') + '\n'; // get the values of data[i] and join them with commas
            fs.promises.appendFile('failed.csv', failedRow);
        }
    }
}

const stitch = async (images, filename, partitions) => {
    const absoluteOutputPath = path.resolve(filename);
    await execFileAsync('/usr/local/bin/vips', ['arrayjoin', images, '--across', partitions, absoluteOutputPath]);
};

const downloadTile = async (bounds) => {
    await getToken();
    const turfBounds = bounds;
    const boundsPoly = turf.bboxPolygon(turfBounds);

    const widthLine = turf.lineString([
        [boundsPoly.geometry.coordinates[0][0][0], boundsPoly.geometry.coordinates[0][0][1]],
        [boundsPoly.geometry.coordinates[0][1][0], boundsPoly.geometry.coordinates[0][1][1]]
    ]);

    const boundsWidth = turf.length(widthLine, {units: 'kilometers'}) * 1000;

    const heightLine = turf.lineString([
        [boundsPoly.geometry.coordinates[0][1][0], boundsPoly.geometry.coordinates[0][1][1]],
        [boundsPoly.geometry.coordinates[0][2][0], boundsPoly.geometry.coordinates[0][2][1]]
    ]);

    const boundsHeight = turf.length(heightLine, {units: 'kilometers'}) * 1000;
    const partitions = computePartitions(boundsWidth, boundsHeight, 1);
    console.log(partitions)
    const cellWidth = (boundsWidth / 1000) / partitions;
    const cellHeight = (boundsHeight / 1000) / partitions;
    const options = {units: 'kilometers'};
    const rectangleGrid = geofunctions.rectangleGrid(turfBounds, cellWidth, cellHeight, options);
    const filenames = [];
    const promises = rectangleGrid.features.map((feature, i) => {
        const bbox = turf.bbox(feature);
        const filename = TMP_DIRECTORY + uuidv4() + ".jpg";
        filenames.push(filename);
        return downloadImage(filename, bbox);
    });
    await Promise.all(promises);

    const uid = uuidv4();
    const newfile = TMP_DIRECTORY + uid + ".jpg";
    const originalfile = TMP_DIRECTORY + uid + "-original.jpg";
    if (partitions === 1) {
        fs.renameSync(filenames[0], originalfile);
        const image = sharp(originalfile);
        await image.jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
        return {cfile: newfile, ofile: originalfile};
    } else {
        await stitch(filenames.join(' '), originalfile, partitions);
        const image = sharp(originalfile);
        const metadata = await image.metadata();
        const width = metadata.width;
        const height = metadata.height;
        const pix = width * height;
        if (pix > MAX_IMAGE_PIXELS) {
            if (width > height) {
                await image.resize({width: MAX_IMAGE_DIMENSION}).jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
            } else {
                await image.resize({height: MAX_IMAGE_DIMENSION}).jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
            }
        } else {
            await image.jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
        }
        cleanup(filenames);
        return {cfile: newfile, ofile: originalfile};
    }
};

const cleanup = (filenames) => {
    filenames.forEach(filename => fs.unlinkSync(filename));
};
const downloadImage = async (filename, bbox) => {
    let bboxcomputed = turf_to_bbox(bbox);
    let size = compute_size(bbox,21);
    let url = `https://aom-us.nearmap.com/arcgis/rest/services/nearmap_us/ImageServer/exportImage?f=image&bbox=${bboxcomputed}&imageSR=102100&bboxSR=4326&size=${size}&adjustAspectRatio=false&token=${TOKEN}`
    console.log(url)
    const response = await fetch(url);
    if (response.ok) {
        const buffer = await response.buffer();
        await fs.promises.writeFile(filename, buffer);
    } else {
        throw new Error(`Image download failed with status ${response.status}`);
    }
};

function computePartitions  (boundsWidth, boundsHeight, partitions)  {
    const cellWidth = (boundsWidth / partitions) / FACTOR;
    const cellHeight = (boundsHeight / partitions) / FACTOR;
    return (cellWidth < 5600 && cellHeight < 5600) ? partitions : computePartitions(boundsWidth, boundsHeight, partitions * 2);
};/*
const makeRequest = async (url, options = {}) => {
    let proxyURL = 'http://ntiles:<EMAIL>:20000';


    let retries = 3;

    while (retries > 0) {
        try {
            const proxyAgent = new HttpsProxyAgent(proxyURL);

            const defaultOptions = {
                "method": "GET",
                agent: proxyAgent
            };
            const response = await Promise.race([fetch(url, { ...defaultOptions, ...options }), new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout occurred')), 10000))]);

            if (response && response.ok) {
                return response;
            } else {
                console.log(`Attempt failed with status code: ${response.status}`);
                retries--;
            }
        } catch (ex) {
            console.log(ex);
            retries--;
        }
    }

    return null;
} */
/*
getToken().then(function (result) {
    console.log(result)
    
}); */
module.exports = {
    downloadTile
};
