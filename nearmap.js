const geofunctions = require('./geofunctions');
const fetch = require('node-fetch');
const turf = require('@turf/turf');
const fs = require('fs');
const { promisify } = require('util');
const { execFile } = require('child_process');
const execFileAsync = promisify(execFile);
const { v4: uuidv4 } = require('uuid');
const sharp = require('sharp');
const db = require('./db')
const path = require('path');
const PRICING_FACTOR = 15000;
const PIXEL_SIZE_FACTOR = 39.37;
const PIXEL_SIZE_THRESHOLD = 15;
const TMP_DIRECTORY = '../tmp/';
const JPEG_QUALITY = 80;
const MAX_IMAGE_PIXELS = 64000000;
const MAX_IMAGE_DIMENSION = 8000;
const FACTOR = 0.0574823;
const SQM_TO_ACRES = 0.000247105;
const apiKey = 'MDcxNjM5ODctNGVkZC00Zjg2LTlhOTgtMjU0MGM2NWM0ZmU3'
const coverageUrl = "https://api.nearmap.com/coverage/v2/poly/$poly?apikey=$apikey&fields=id,captureDate,pixelSize&since=2021-05-05&limit=5"
const imageUrl = "http://us0.nearmap.com/staticmap?bbox=$bbox&date=$date&zoom=$zoom&httpauth=false&apikey=$apikey"


const stitch = async (images, filename, partitions) => {
    const absoluteOutputPath = path.resolve(filename);
    console.log(absoluteOutputPath)
    await execFileAsync('/usr/local/bin/vips', ['arrayjoin', images, '--across', partitions, absoluteOutputPath]);
};

const getImageryDetails = async (bounds) => {
    const nearmapPoly = geofunctions.convertCoordsToNearMap(bounds);
    let url = coverageUrl.replace('$apikey', apiKey).replace('$poly', nearmapPoly);
    const response = await fetch(url);
    if (response.ok) {
        const body = await response.json();
        return await parseNearMap(body, bounds);
    } else {
        throw new Error(`Request failed with status ${response.status}`);
    }
};

const parseNearMap = async (data, bounds) => {
    let returnData = [];
    if (data.surveys) {
        let newArray = data.surveys.filter(el => el.pixelSize * PIXEL_SIZE_FACTOR <= PIXEL_SIZE_THRESHOLD);
        newArray.sort((a, b) => a.pixelSize - b.pixelSize);
        newArray.forEach(element => {
            const obj = {
                date: element.captureDate,
                resolution: element.pixelSize * PIXEL_SIZE_FACTOR,
                isContained: true,
                id: element.id,
                bboxpoly: bounds.geometry,
            }
            returnData.push(obj);
        });
    }
    return returnData;
};

const getStatus = async (boundsPoly) => {
    const boundsArea = turf.area(boundsPoly);
    try {
        const data = await getImageryDetails(boundsPoly);
        const dataEstimate = boundsArea / PRICING_FACTOR;
        const areaInAcres = boundsArea * SQM_TO_ACRES;
        let dollarEstimate = Math.ceil((areaInAcres * 0.25) * 2) / 2;
        if (data.length > 0 && dollarEstimate < 60) {
            if (dollarEstimate < 1)
                dollarEstimate = 1;
            return {
                error: false,
                provider: "Nearmap",
                data: dataEstimate,
                estimate: dollarEstimate,
                im: data,
            };
        } else {
            return {
                error: true,
                detail: "No Imagery",
                provider: "Nearmap",
                data: null,
                estimate: null,
                im: [],
            };
        }
    } catch (error) {
        throw error;
    }
};

const computePartitions = (boundsWidth, boundsHeight, partitions) => {
    const cellWidth = (boundsWidth / partitions) / FACTOR;
    const cellHeight = (boundsHeight / partitions) / FACTOR;
    return (cellWidth < 5600 && cellHeight < 5600) ? partitions : computePartitions(boundsWidth, boundsHeight, partitions * 2);
};


const cleanup = (filenames) => {
    filenames.forEach(filename => fs.unlinkSync(filename));
};

const computeAreaInAcresUsingTurf = (bbox) => {
    let [minLat, minLon, maxLat, maxLon] = bbox.split(',').map(parseFloat);
    
    let boxPolygon = turf.bboxPolygon([minLon, minLat, maxLon, maxLat]);

    let areaInSqm = turf.area(boxPolygon);

    let areaInAcres = areaInSqm * SQM_TO_ACRES;

    return areaInAcres;
}

const downloadImage = async (filename, bbox, zoom, date, jobID, accessCode, worker, bid, cost, mid) => {
    let url = imageUrl.replace('$apikey', apiKey).replace('$bbox', bbox).replace('$zoom', zoom).replace('$date', date);
    const response = await fetch(url);
    console.log(url)
    if (response.ok) {
        const buffer = await response.buffer();
        const uncompressedSize = buffer.length; 
        let areaInAcres = computeAreaInAcresUsingTurf(bbox);
        await db.logNearMap(zoom, date, jobID, accessCode, worker, bid,cost, mid, uncompressedSize, url, areaInAcres)
        await fs.promises.writeFile(filename, buffer);
    } else {
        throw new Error(`Image download failed with status ${response.status}`);
    }
};


const downloadTile = async (data) => {
    const dt = data.date;
    const requestUUID = uuidv4();
    const date = dt.replace(/-/g, '');
    const turfBounds = data.bounds;
    const boundsPoly = turf.bboxPolygon(turfBounds);

    const widthLine = turf.lineString([
        [boundsPoly.geometry.coordinates[0][0][0], boundsPoly.geometry.coordinates[0][0][1]],
        [boundsPoly.geometry.coordinates[0][1][0], boundsPoly.geometry.coordinates[0][1][1]]
    ]);

    const boundsWidth = turf.length(widthLine, {units: 'kilometers'}) * 1000;

    const heightLine = turf.lineString([
        [boundsPoly.geometry.coordinates[0][1][0], boundsPoly.geometry.coordinates[0][1][1]],
        [boundsPoly.geometry.coordinates[0][2][0], boundsPoly.geometry.coordinates[0][2][1]]
    ]);

    const boundsHeight = turf.length(heightLine, {units: 'kilometers'}) * 1000;
    const partitions = computePartitions(boundsWidth, boundsHeight, 1);
    const cellWidth = (boundsWidth / 1000) / partitions;
    const cellHeight = (boundsHeight / 1000) / partitions;
    const options = {units: 'kilometers'};
    const rectangleGrid = geofunctions.rectangleGrid(turfBounds, cellWidth, cellHeight, options);
    const filenames = [];
    const promises = rectangleGrid.features.map((feature, i) => {
        const bbox = turf.bbox(feature);
        const filename = TMP_DIRECTORY + uuidv4() + ".jpg";
        filenames.push(filename);
        return downloadImage(filename, geofunctions.convertBoundsToNearMap(bbox), 24, date, requestUUID, data.accessCode, data.worker, data.bid, data.cost, data.mid);
    });
    await Promise.all(promises);

    const uid = uuidv4();
    const newfile = TMP_DIRECTORY + uid + ".jpg";
    const originalfile = TMP_DIRECTORY + uid + "-original.jpg";
    if (partitions === 1) {
        fs.renameSync(filenames[0], originalfile);
        const image = sharp(originalfile);
        await image.jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
        return {cfile: newfile, ofile: originalfile};
    } else {
        await stitch(filenames.join(' '), originalfile, partitions);
        const image = sharp(originalfile);
        const metadata = await image.metadata();
        const width = metadata.width;
        const height = metadata.height;
        const pix = width * height;
        if (pix > MAX_IMAGE_PIXELS) {
            if (width > height) {
                await image.resize({width: MAX_IMAGE_DIMENSION}).jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
            } else {
                await image.resize({height: MAX_IMAGE_DIMENSION}).jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
            }
        } else {
            await image.jpeg({quality: JPEG_QUALITY, progressive: true}).toFile(newfile);
        }
        cleanup(filenames);
        return {cfile: newfile, ofile: originalfile};
    }
};

module.exports = {
    downloadTile,
    getStatus
};