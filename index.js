const express = require('express')
const generalrouter = require('./generalrouter')
const tilesrouter = require('./tilesrouter')
const nearmaptest = require('./nearmaptest')
const app = express()
const port = 3001
const ip = '127.0.0.1'

app.use(express.json({
  limit: '50mb',
  type: ['application/json', 'text/plain']
}))
app.use(express.urlencoded({
  limit: '50mb',
  extended: true
}))

app.use('/api2/ntest', nearmaptest)
app.use('/api2/general', generalrouter)
app.use('/api2/tiles', tilesrouter)
app.get('/api2', (request, response) => {
  response.json({ Version: '2' })
})



app.listen(port, ip, () => {
  console.log(`App running on port ${port}.`)
})
