const express = require('express')
const router = express.Router()
const db = require('./db')
const tiles = require('./tiles')
const storage = require('./storage')
const multer = require('multer');
const geofunctions = require('./geofunctions');
const turf = require('@turf/turf');
const booleanIntersects = require('@turf/boolean-intersects').default;
const upload = multer();

const eagleview = require('./eagleview');
const jwt = require('jsonwebtoken');



router.get('/', function (req, res) {
    res.send('Not applicable')
})

router.post('/upload-external-tile', upload.single('file'), async function (req, res) {
    try {
        const date = req.query.date;
        const bounds = req.query.bounds;
        const accessCode = req.query.accessCode;
        const type = req.query.type || '.png'
        const url = await tiles.uploadExternal(req.file.buffer, accessCode,bounds,date,type);
        if(url)
            res.json({url: url})
        else
            res.sendStatus(500);

    } catch (ex)
    {
        console.log(ex)
        res.sendStatus(500)
    }
    

})

router.get('/searchcached', function (req, res) {
    tiles.getCached(req.query.accesscode, req.query.bounds).then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/searchcachedbyurl', function (req, res) {
    tiles.getURLCached(req.query.url).then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/searchcachedbyurlmapbuilder', function (req, res) {
    tiles.getURLCached(req.query.url).then(function (result) {
        db.copyTile(req.query.accesscode, req.query.url)
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/search', function (req, res) {
  
    tiles.searchProviders(req.query.bounds, req.query.accesscode, req.query.worker).then(function (result) {
        res.send(result)
    }).catch(function (e) {
		console.log(e)
        res.send(e)
    })
})



router.get('/downloadnearmaptile', function (req, res) { 
   
    tiles.downloadNearMapTile(req.query.bounds, req.query.date, req.query.accesscode, req.query.worker, req.query.bid, req.query.cost, req.query.mid).then(function (result) {
        res.send(result)
    }).catch(function (e) {
		
        res.send(e)
    })
})



router.get('/syncstorage', function (req, res) {
    
    storage.syncStorage().then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/getcachedtile', function(req, res) {
    tiles.getCachedTileByID(req.query.accesscode, req.query.tileid, req.query.worker, req.query.bid, req.query.mid).then(function (result) {
        res.send(result)
    }).catch(function (e){
        res.send(e)
    })
})










router.post('/eagleview/purchase', async (req, res) => {
    try {
        const { accesscode, layer, bounds, tileMatrixSet } = req.body;
        console.log('EagleView Purchase Request:', req.body);
        if (!accesscode || !layer || !bounds || !tileMatrixSet) {
            return res.status(400).send('Missing required parameters');
        }

        const result = await db.logEagleViewPurchase(accesscode, layer, bounds, tileMatrixSet);

        if (result.error) {
            return res.status(500).send('Error logging purchase');
        }

        // After new purchase, get all purchases for this layer to put in the token
        const { bounds: allPurchasedBounds } = await db.getEagleViewPurchases(accesscode, layer);

        // Generate JWT token for the purchased layer
        const token = jwt.sign(
            {
                accesscode,
                layer,
                bounds: allPurchasedBounds, // Pass all purchased bounds
                tileMatrixSet,
                exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24) // Expires in 24 hours
            },
            process.env.JWT_SECRET || '54a6f406-5cf2-46b7-843c-079f6084f8ba'
        );

        res.status(201).json({
            purchaseId: result.id,
            token: token
        });
    } catch (error) {
        console.error(error);
        res.status(500).send('Error processing purchase');
    }
});
router.get('/eagleview/capabilities', async (req, res) => {
    try {
        const capabilities = await eagleview.getCapabilities(req.query.bounds);
        res.json(capabilities);
    } catch (error) {
        console.error(error);
        res.status(500).send('Error fetching EagleView capabilities');
    }
});

router.get('/eagleview/tile/:layer/:z/:x/:y.:format', async (req, res) => {
    try {
        const { layer, z, x, y, format } = req.params;
        const token = req.query.token;

        // Verify JWT token
        let decoded;
        try {
            decoded = jwt.verify(token, process.env.JWT_SECRET || 'eagleview_secret');
        } catch (err) {
            return res.status(401).send('Unauthorized: Invalid token');
        }
        
        // Check if the requested layer matches the token
        if (decoded.layer !== layer) {
            return res.status(403).send('Forbidden: Layer mismatch');
        }
        
        // Get bounds from token
        const purchasedBounds = decoded.bounds;
        const tileMatrixSet = decoded.tileMatrixSet;
        
        if (!purchasedBounds || purchasedBounds.length === 0) {
            return res.status(403).send('Forbidden: No purchased bounds found for this layer');
        }

        const tilePolygon = geofunctions.tileToBoundingBox(parseInt(z), parseInt(x), parseInt(y));
        const hasAccess = purchasedBounds.some(bounds => {
            const purchasedPolygon = turf.polygon([bounds]);
            return booleanIntersects(tilePolygon, purchasedPolygon);
        });

        if (!hasAccess) {
            return res.status(403).send('Forbidden: Tile is outside of purchased bounds');
        }

       
        // Set caching headers to cache for one year
        res.set('Cache-Control', 'public, max-age=31536000');

        const tileStream = await eagleview.getTile({ layer, z, x, y, format, accesscode: decoded.accesscode, tileMatrixSet });
        
        tileStream.pipe(res);
    } catch (error) {
        console.error(error);
        res.status(500).send('Error fetching EagleView tile');
    }
});
module.exports = router