{"name": "api", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "api", "version": "1.0.0", "license": "ISC", "dependencies": {"@turf/boolean-intersects": "^7.2.0", "@turf/turf": "^5.1.6", "express": "^4.17.1", "fast-xml-parser": "^5.2.5", "geodesy": "^1.1.3", "gm": "^1.23.1", "https-proxy-agent": "^7.0.2", "jsonwebtoken": "^9.0.2", "minio": "^7.0.14", "mkdirp": "^0.5.1", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.11", "papaparse": "^5.4.1", "pg-promise": "^10.7.4", "proj4": "^2.9.0", "proper-lockfile": "^4.1.2", "redis": "^2.8.0", "request": "^2.88.2", "sharp": "^0.34.2", "url": "^0.11.0", "uuid": "^3.4.0", "valid-url": "^1.0.9", "wasm-vips": "^0.0.13", "write-file-atomic": "^5.0.1"}}, "node_modules/@emnapi/runtime": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.3.tgz", "integrity": "sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==", "license": "MIT", "optional": true, "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@img/sharp-darwin-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.2.tgz", "integrity": "sha512-OfXHZPppddivUJnqyKoi5YVeHRkkNE2zUFT2gbpKxp/JZCFYEYubnMg+gOp6lWfasPrTS+KPosKqdI+ELYVDtg==", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["darwin"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0"}}, "node_modules/@img/sharp-darwin-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-darwin-x64/-/sharp-darwin-x64-0.34.2.tgz", "integrity": "sha512-dYvWqmjU9VxqXmjEtjmvHnGqF8GrVjM2Epj9rJ6BUIXvk8slvNDJbhGFvIoXzkDhrJC2jUxNLz/GUjjvSzfw+g==", "cpu": ["x64"], "license": "Apache-2.0", "optional": true, "os": ["darwin"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-darwin-x64": "1.1.0"}}, "node_modules/@img/sharp-libvips-darwin-arm64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-arm64/-/sharp-libvips-darwin-arm64-1.1.0.tgz", "integrity": "sha512-HZ/JUmPwrJSoM4DIQPv/BfNh9yrOA8tlBbqbLz4JZ5uew2+o22Ik+tHQJcih7QJuSa0zo5coHTfD5J8inqj9DA==", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["darwin"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-darwin-x64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-x64/-/sharp-libvips-darwin-x64-1.1.0.tgz", "integrity": "sha512-Xzc2ToEmHN+hfvsl9wja0RlnXEgpKNmftriQp6XzY/RaSfwD9th+MSh0WQKzUreLKKINb3afirxW7A0fz2YWuQ==", "cpu": ["x64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["darwin"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-arm": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm/-/sharp-libvips-linux-arm-1.1.0.tgz", "integrity": "sha512-s8BAd0lwUIvYCJyRdFqvsj+BJIpDBSxs6ivrOPm/R7piTs5UIwY5OjXrP2bqXC9/moGsyRa37eYWYCOGVXxVrA==", "cpu": ["arm"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-arm64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm64/-/sharp-libvips-linux-arm64-1.1.0.tgz", "integrity": "sha512-IVfGJa7gjChDET1dK9SekxFFdflarnUB8PwW8aGwEoF3oAsSDuNUTYS+SKDOyOJxQyDC1aPFMuRYLoDInyV9Ew==", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-ppc64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-ppc64/-/sharp-libvips-linux-ppc64-1.1.0.tgz", "integrity": "sha512-tiXxFZFbhnkWE2LA8oQj7KYR+bWBkiV2nilRldT7bqoEZ4HiDOcePr9wVDAZPi/Id5fT1oY9iGnDq20cwUz8lQ==", "cpu": ["ppc64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-s390x": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-s390x/-/sharp-libvips-linux-s390x-1.1.0.tgz", "integrity": "sha512-xukSwvhguw7COyzvmjydRb3x/09+21HykyapcZchiCUkTThEQEOMtBj9UhkaBRLuBrgLFzQ2wbxdeCCJW/jgJA==", "cpu": ["s390x"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-x64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-x64/-/sharp-libvips-linux-x64-1.1.0.tgz", "integrity": "sha512-yRj2+reB8iMg9W5sULM3S74jVS7zqSzHG3Ol/twnAAkAhnGQnpjj6e4ayUz7V+FpKypwgs82xbRdYtchTTUB+Q==", "cpu": ["x64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linuxmusl-arm64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-arm64/-/sharp-libvips-linuxmusl-arm64-1.1.0.tgz", "integrity": "sha512-jYZdG+whg0MDK+q2COKbYidaqW/WTz0cc1E+tMAusiDygrM4ypmSCjOJPmFTvHHJ8j/6cAGyeDWZOsK06tP33w==", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linuxmusl-x64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-x64/-/sharp-libvips-linuxmusl-x64-1.1.0.tgz", "integrity": "sha512-wK7SBdwrAiycjXdkPnGCPLjYb9lD4l6Ze2gSdAGVZrEL05AOUJESWU2lhlC+Ffn5/G+VKuSm6zzbQSzFX/P65A==", "cpu": ["x64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-linux-arm": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm/-/sharp-linux-arm-0.34.2.tgz", "integrity": "sha512-0DZzkvuEOqQUP9mo2kjjKNok5AmnOr1jB2XYjkaoNRwpAYMDzRmAqUIa1nRi58S2WswqSfPOWLNOr0FDT3H5RQ==", "cpu": ["arm"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-arm": "1.1.0"}}, "node_modules/@img/sharp-linux-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm64/-/sharp-linux-arm64-0.34.2.tgz", "integrity": "sha512-D8n8wgWmPDakc83LORcfJepdOSN6MvWNzzz2ux0MnIbOqdieRZwVYY32zxVx+IFUT8er5KPcyU3XXsn+GzG/0Q==", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-arm64": "1.1.0"}}, "node_modules/@img/sharp-linux-s390x": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.2.tgz", "integrity": "sha512-EGZ1xwhBI7dNISwxjChqBGELCWMGDvmxZXKjQRuqMrakhO8QoMgqCrdjnAqJq/CScxfRn+Bb7suXBElKQpPDiw==", "cpu": ["s390x"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0"}}, "node_modules/@img/sharp-linux-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-x64/-/sharp-linux-x64-0.34.2.tgz", "integrity": "sha512-sD7J+h5nFLMMmOXYH4DD9UtSNBD05tWSSdWAcEyzqW8Cn5UxXvsHAxmxSesYUsTOBmUnjtxghKDl15EvfqLFbQ==", "cpu": ["x64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-x64": "1.1.0"}}, "node_modules/@img/sharp-linuxmusl-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.2.tgz", "integrity": "sha512-NEE2vQ6wcxYav1/A22OOxoSOGiKnNmDzCYFOZ949xFmrWZOVII1Bp3NqVVpvj+3UeHMFyN5eP/V5hzViQ5CZNA==", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0"}}, "node_modules/@img/sharp-linuxmusl-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-x64/-/sharp-linuxmusl-x64-0.34.2.tgz", "integrity": "sha512-DOYMrDm5E6/8bm/yQLCWyuDJwUnlevR8xtF8bs+gjZ7cyUNYXiSf/E8Kp0Ss5xasIaXSHzb888V1BE4i1hFhAA==", "cpu": ["x64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linuxmusl-x64": "1.1.0"}}, "node_modules/@img/sharp-wasm32": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.2.tgz", "integrity": "sha512-/VI4mdlJ9zkaq53MbIG6rZY+QRN3MLbR6usYlgITEzi4Rpx5S6LFKsycOQjkOGmqTNmkIdLjEvooFKwww6OpdQ==", "cpu": ["wasm32"], "license": "Apache-2.0 AND LGPL-3.0-or-later AND MIT", "optional": true, "dependencies": {"@emnapi/runtime": "^1.4.3"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-win32-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-win32-arm64/-/sharp-win32-arm64-0.34.2.tgz", "integrity": "sha512-cfP/r9FdS63VA5k0xiqaNaEoGxBg9k7uE+RQGzuK9fHt7jib4zAVVseR9LsE4gJcNWgT6APKMNnCcnyOtmSEUQ==", "cpu": ["arm64"], "license": "Apache-2.0 AND LGPL-3.0-or-later", "optional": true, "os": ["win32"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-win32-ia32": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.2.tgz", "integrity": "sha512-QLjGGvAbj0X/FXl8n1WbtQ6iVBpWU7JO94u/P2M4a8CFYsvQi4GW2mRy/JqkRx0qpBzaOdKJKw8uc930EX2AHw==", "cpu": ["ia32"], "license": "Apache-2.0 AND LGPL-3.0-or-later", "optional": true, "os": ["win32"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-win32-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-win32-x64/-/sharp-win32-x64-0.34.2.tgz", "integrity": "sha512-aUdT6zEYtDKCaxkofmmJDJYGCf0+pJg3eU9/oBuqvEeoB9dKI6ZLc/1iLJCTuJQDO4ptntAlkUmHgGjyuobZbw==", "cpu": ["x64"], "license": "Apache-2.0 AND LGPL-3.0-or-later", "optional": true, "os": ["win32"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@turf/along": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bearing": "^5.1.5", "@turf/destination": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/area": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/bbox": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/bbox-clip": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "lineclip": "^1.1.5"}}, "node_modules/@turf/bbox-polygon": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/bearing": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/bezier-spline": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/boolean-clockwise": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/boolean-contains": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/boolean-point-in-polygon": "^5.1.5", "@turf/boolean-point-on-line": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/boolean-crosses": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-point-in-polygon": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-intersect": "^5.1.5", "@turf/polygon-to-line": "^5.1.5"}}, "node_modules/@turf/boolean-disjoint": {"version": "5.1.6", "license": "MIT", "dependencies": {"@turf/boolean-point-in-polygon": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/line-intersect": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/polygon-to-line": "^5.1.5"}}, "node_modules/@turf/boolean-equal": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clean-coords": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "geojson-equality": "0.1.6"}}, "node_modules/@turf/boolean-intersects": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/boolean-intersects/-/boolean-intersects-7.2.0.tgz", "integrity": "sha512-GLRyLQgK3F14drkK5Qi9Mv7Z9VT1bgQUd9a3DB3DACTZWDSwfh8YZUFn/HBwRkK8dDdgNEXaavggQHcPi1k9ow==", "license": "MIT", "dependencies": {"@turf/boolean-disjoint": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/meta": "^7.2.0", "@types/geojson": "^7946.0.10", "tslib": "^2.8.1"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-intersects/node_modules/@turf/boolean-disjoint": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/boolean-disjoint/-/boolean-disjoint-7.2.0.tgz", "integrity": "sha512-xdz+pYKkLMuqkNeJ6EF/3OdAiJdiHhcHCV0ykX33NIuALKIEpKik0+NdxxNsZsivOW6keKwr61SI+gcVtHYcnQ==", "license": "MIT", "dependencies": {"@turf/boolean-point-in-polygon": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/line-intersect": "^7.2.0", "@turf/meta": "^7.2.0", "@turf/polygon-to-line": "^7.2.0", "@types/geojson": "^7946.0.10", "tslib": "^2.8.1"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-intersects/node_modules/@turf/boolean-point-in-polygon": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/boolean-point-in-polygon/-/boolean-point-in-polygon-7.2.0.tgz", "integrity": "sha512-lvEOjxeXIp+wPXgl9kJA97dqzMfNexjqHou+XHVcfxQgolctoJiRYmcVCWGpiZ9CBf/CJha1KmD1qQoRIsjLaA==", "license": "MIT", "dependencies": {"@turf/helpers": "^7.2.0", "@turf/invariant": "^7.2.0", "@types/geojson": "^7946.0.10", "point-in-polygon-hao": "^1.1.0", "tslib": "^2.8.1"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-intersects/node_modules/@turf/helpers": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/helpers/-/helpers-7.2.0.tgz", "integrity": "sha512-cXo7b<PERSON><PERSON>Zoa7aC7ydLmUR02oB3IgDe7MxiPuRz3cCtYQHn+BJ6h1tihmamYDWWUlPHgSNF0i3ATc4WmDECZafKw==", "license": "MIT", "dependencies": {"@types/geojson": "^7946.0.10", "tslib": "^2.8.1"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-intersects/node_modules/@turf/invariant": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/invariant/-/invariant-7.2.0.tgz", "integrity": "sha512-kV4u8e7Gkpq+kPbAKNC21CmyrXzlbBgFjO1PhrHPgEdNqXqDawoZ3i6ivE3ULJj2rSesCjduUaC/wyvH/sNr2Q==", "license": "MIT", "dependencies": {"@turf/helpers": "^7.2.0", "@types/geojson": "^7946.0.10", "tslib": "^2.8.1"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-intersects/node_modules/@turf/line-intersect": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/line-intersect/-/line-intersect-7.2.0.tgz", "integrity": "sha512-GhCJVEkc8EmggNi85EuVLoXF5T5jNVxmhIetwppiVyJzMrwkYAkZSYB3IBFYGUUB9qiNFnTwungVSsBV/S8ZiA==", "license": "MIT", "dependencies": {"@turf/helpers": "^7.2.0", "@types/geojson": "^7946.0.10", "sweepline-intersections": "^1.5.0", "tslib": "^2.8.1"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-intersects/node_modules/@turf/meta": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/meta/-/meta-7.2.0.tgz", "integrity": "sha512-igzTdHsQc8TV1RhPuOLVo74Px/hyPrVgVOTgjWQZzt3J9BVseCdpfY/0cJBdlSRI4S/yTmmHl7gAqjhpYH5Yaw==", "license": "MIT", "dependencies": {"@turf/helpers": "^7.2.0", "@types/geojson": "^7946.0.10"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-intersects/node_modules/@turf/polygon-to-line": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@turf/polygon-to-line/-/polygon-to-line-7.2.0.tgz", "integrity": "sha512-9jeTN3LiJ933I5sd4K0kwkcivOYXXm1emk0dHorwXeSFSHF+nlYesEW3Hd889wb9lZd7/SVLMUeX/h39mX+vCA==", "license": "MIT", "dependencies": {"@turf/helpers": "^7.2.0", "@turf/invariant": "^7.2.0", "@types/geojson": "^7946.0.10", "tslib": "^2.8.1"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/boolean-overlap": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-intersect": "^5.1.5", "@turf/line-overlap": "^5.1.5", "@turf/meta": "^5.1.5", "geojson-equality": "0.1.6"}}, "node_modules/@turf/boolean-parallel": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clean-coords": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/line-segment": "^5.1.5", "@turf/rhumb-bearing": "^5.1.5"}}, "node_modules/@turf/boolean-point-in-polygon": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/boolean-point-on-line": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/boolean-within": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/boolean-point-in-polygon": "^5.1.5", "@turf/boolean-point-on-line": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/buffer": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/center": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/projection": "^5.1.5", "d3-geo": "1.7.1", "turf-jsts": "*"}}, "node_modules/@turf/center": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/center-mean": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/center-median": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/center-mean": "^5.1.5", "@turf/centroid": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/center-of-mass": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/centroid": "^5.1.5", "@turf/convex": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/centroid": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/circle": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/destination": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/clean-coords": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/clone": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/clusters": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/clusters-dbscan": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clone": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "density-clustering": "1.3.0"}}, "node_modules/@turf/clusters-kmeans": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "skmeans": "0.9.7"}}, "node_modules/@turf/collect": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/boolean-point-in-polygon": "^5.1.5", "@turf/helpers": "^5.1.5", "rbush": "^2.0.1"}}, "node_modules/@turf/combine": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/concave": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clone": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/tin": "^5.1.5", "topojson-client": "3.x", "topojson-server": "3.x"}}, "node_modules/@turf/convex": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5", "concaveman": "*"}}, "node_modules/@turf/destination": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/difference": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/area": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "turf-jsts": "*"}}, "node_modules/@turf/dissolve": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-overlap": "^5.1.5", "@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-intersect": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/union": "^5.1.5", "geojson-rbush": "2.1.0", "get-closest": "*"}}, "node_modules/@turf/distance": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/ellipse": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/rhumb-destination": "^5.1.5", "@turf/transform-rotate": "^5.1.5"}}, "node_modules/@turf/envelope": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/bbox-polygon": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/explode": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/flatten": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/flip": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/great-circle": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/helpers": {"version": "5.1.5", "license": "MIT"}, "node_modules/@turf/hex-grid": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/intersect": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/interpolate": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/centroid": "^5.1.5", "@turf/clone": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/hex-grid": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/point-grid": "^5.1.5", "@turf/square-grid": "^5.1.5", "@turf/triangle-grid": "^5.1.5"}}, "node_modules/@turf/intersect": {"version": "5.1.6", "license": "MIT", "dependencies": {"@turf/clean-coords": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/truncate": "^5.1.5", "turf-jsts": "*"}}, "node_modules/@turf/invariant": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/isobands": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/area": "^5.1.5", "@turf/bbox": "^5.1.5", "@turf/boolean-point-in-polygon": "^5.1.5", "@turf/explode": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/isolines": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/kinks": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/length": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/line-arc": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/circle": "^5.1.5", "@turf/destination": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/line-chunk": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/length": "^5.1.5", "@turf/line-slice-along": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/line-intersect": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-segment": "^5.1.5", "@turf/meta": "^5.1.5", "geojson-rbush": "2.1.0"}}, "node_modules/@turf/line-offset": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/line-overlap": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-point-on-line": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-segment": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/nearest-point-on-line": "^5.1.5", "geojson-rbush": "2.1.0"}}, "node_modules/@turf/line-segment": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/line-slice": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/nearest-point-on-line": "^5.1.5"}}, "node_modules/@turf/line-slice-along": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bearing": "^5.1.5", "@turf/destination": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/line-split": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-intersect": "^5.1.5", "@turf/line-segment": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/nearest-point-on-line": "^5.1.5", "@turf/square": "^5.1.5", "@turf/truncate": "^5.1.5", "geojson-rbush": "2.1.0"}}, "node_modules/@turf/line-to-polygon": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/mask": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/union": "^5.1.5", "rbush": "^2.0.1"}}, "node_modules/@turf/meta": {"version": "5.1.6", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/midpoint": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bearing": "^5.1.5", "@turf/destination": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/nearest-point": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clone": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/nearest-point-on-line": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bearing": "^5.1.5", "@turf/destination": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-intersect": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/nearest-point-to-line": {"version": "5.1.6", "license": "MIT", "dependencies": {"@turf/helpers": "6.x", "@turf/invariant": "6.x", "@turf/meta": "6.x", "@turf/point-to-line-distance": "^5.1.5", "object-assign": "*"}}, "node_modules/@turf/nearest-point-to-line/node_modules/@turf/helpers": {"version": "6.1.4", "license": "MIT"}, "node_modules/@turf/nearest-point-to-line/node_modules/@turf/invariant": {"version": "6.1.2", "license": "MIT", "dependencies": {"@turf/helpers": "6.x"}}, "node_modules/@turf/nearest-point-to-line/node_modules/@turf/meta": {"version": "6.0.2", "license": "MIT", "dependencies": {"@turf/helpers": "6.x"}}, "node_modules/@turf/planepoint": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/point-grid": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-within": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/point-on-feature": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-point-in-polygon": "^5.1.5", "@turf/center": "^5.1.5", "@turf/explode": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/nearest-point": "^5.1.5"}}, "node_modules/@turf/point-to-line-distance": {"version": "5.1.6", "license": "MIT", "dependencies": {"@turf/bearing": "6.x", "@turf/distance": "6.x", "@turf/helpers": "6.x", "@turf/invariant": "6.x", "@turf/meta": "6.x", "@turf/projection": "6.x", "@turf/rhumb-bearing": "6.x", "@turf/rhumb-distance": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/bearing": {"version": "6.0.1", "license": "MIT", "dependencies": {"@turf/helpers": "6.x", "@turf/invariant": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/clone": {"version": "6.0.2", "license": "MIT", "dependencies": {"@turf/helpers": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/distance": {"version": "6.0.1", "license": "MIT", "dependencies": {"@turf/helpers": "6.x", "@turf/invariant": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/helpers": {"version": "6.1.4", "license": "MIT"}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/invariant": {"version": "6.1.2", "license": "MIT", "dependencies": {"@turf/helpers": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/meta": {"version": "6.0.2", "license": "MIT", "dependencies": {"@turf/helpers": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/projection": {"version": "6.0.1", "license": "MIT", "dependencies": {"@turf/clone": "6.x", "@turf/helpers": "6.x", "@turf/meta": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/rhumb-bearing": {"version": "6.0.1", "license": "MIT", "dependencies": {"@turf/helpers": "6.x", "@turf/invariant": "6.x"}}, "node_modules/@turf/point-to-line-distance/node_modules/@turf/rhumb-distance": {"version": "6.0.1", "license": "MIT", "dependencies": {"@turf/helpers": "6.x", "@turf/invariant": "6.x"}}, "node_modules/@turf/points-within-polygon": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-point-in-polygon": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/polygon-tangents": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/polygon-to-line": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/polygonize": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-point-in-polygon": "^5.1.5", "@turf/envelope": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/projection": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/random": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/rewind": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-clockwise": "^5.1.5", "@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/rhumb-bearing": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/rhumb-destination": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/rhumb-distance": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/sample": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/sector": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/circle": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/line-arc": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/shortest-path": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/bbox-polygon": "^5.1.5", "@turf/boolean-point-in-polygon": "^5.1.5", "@turf/clean-coords": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/transform-scale": "^5.1.5"}}, "node_modules/@turf/simplify": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clean-coords": "^5.1.5", "@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/square": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5"}}, "node_modules/@turf/square-grid": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-contains": "^5.1.5", "@turf/boolean-overlap": "^5.1.5", "@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/intersect": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/standard-deviational-ellipse": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/center-mean": "^5.1.5", "@turf/ellipse": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/points-within-polygon": "^5.1.5"}}, "node_modules/@turf/tag": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/boolean-point-in-polygon": "^5.1.5", "@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/tesselate": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "earcut": "^2.0.0"}}, "node_modules/@turf/tin": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5"}}, "node_modules/@turf/transform-rotate": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/centroid": "^5.1.5", "@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/rhumb-bearing": "^5.1.5", "@turf/rhumb-destination": "^5.1.5", "@turf/rhumb-distance": "^5.1.5"}}, "node_modules/@turf/transform-scale": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/bbox": "^5.1.5", "@turf/center": "^5.1.5", "@turf/centroid": "^5.1.5", "@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/rhumb-bearing": "^5.1.5", "@turf/rhumb-destination": "^5.1.5", "@turf/rhumb-distance": "^5.1.5"}}, "node_modules/@turf/transform-translate": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/clone": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "@turf/meta": "^5.1.5", "@turf/rhumb-destination": "^5.1.5"}}, "node_modules/@turf/triangle-grid": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/intersect": "^5.1.5", "@turf/invariant": "^5.1.5"}}, "node_modules/@turf/truncate": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}}, "node_modules/@turf/turf": {"version": "5.1.6", "license": "MIT", "dependencies": {"@turf/along": "5.1.x", "@turf/area": "5.1.x", "@turf/bbox": "5.1.x", "@turf/bbox-clip": "5.1.x", "@turf/bbox-polygon": "5.1.x", "@turf/bearing": "5.1.x", "@turf/bezier-spline": "5.1.x", "@turf/boolean-clockwise": "5.1.x", "@turf/boolean-contains": "5.1.x", "@turf/boolean-crosses": "5.1.x", "@turf/boolean-disjoint": "5.1.x", "@turf/boolean-equal": "5.1.x", "@turf/boolean-overlap": "5.1.x", "@turf/boolean-parallel": "5.1.x", "@turf/boolean-point-in-polygon": "5.1.x", "@turf/boolean-point-on-line": "5.1.x", "@turf/boolean-within": "5.1.x", "@turf/buffer": "5.1.x", "@turf/center": "5.1.x", "@turf/center-mean": "5.1.x", "@turf/center-median": "5.1.x", "@turf/center-of-mass": "5.1.x", "@turf/centroid": "5.1.x", "@turf/circle": "5.1.x", "@turf/clean-coords": "5.1.x", "@turf/clone": "5.1.x", "@turf/clusters": "5.1.x", "@turf/clusters-dbscan": "5.1.x", "@turf/clusters-kmeans": "5.1.x", "@turf/collect": "5.1.x", "@turf/combine": "5.1.x", "@turf/concave": "5.1.x", "@turf/convex": "5.1.x", "@turf/destination": "5.1.x", "@turf/difference": "5.1.x", "@turf/dissolve": "5.1.x", "@turf/distance": "5.1.x", "@turf/ellipse": "5.1.x", "@turf/envelope": "5.1.x", "@turf/explode": "5.1.x", "@turf/flatten": "5.1.x", "@turf/flip": "5.1.x", "@turf/great-circle": "5.1.x", "@turf/helpers": "5.1.x", "@turf/hex-grid": "5.1.x", "@turf/interpolate": "5.1.x", "@turf/intersect": "5.1.x", "@turf/invariant": "5.1.x", "@turf/isobands": "5.1.x", "@turf/isolines": "5.1.x", "@turf/kinks": "5.1.x", "@turf/length": "5.1.x", "@turf/line-arc": "5.1.x", "@turf/line-chunk": "5.1.x", "@turf/line-intersect": "5.1.x", "@turf/line-offset": "5.1.x", "@turf/line-overlap": "5.1.x", "@turf/line-segment": "5.1.x", "@turf/line-slice": "5.1.x", "@turf/line-slice-along": "5.1.x", "@turf/line-split": "5.1.x", "@turf/line-to-polygon": "5.1.x", "@turf/mask": "5.1.x", "@turf/meta": "5.1.x", "@turf/midpoint": "5.1.x", "@turf/nearest-point": "5.1.x", "@turf/nearest-point-on-line": "5.1.x", "@turf/nearest-point-to-line": "5.1.x", "@turf/planepoint": "5.1.x", "@turf/point-grid": "5.1.x", "@turf/point-on-feature": "5.1.x", "@turf/point-to-line-distance": "5.1.x", "@turf/points-within-polygon": "5.1.x", "@turf/polygon-tangents": "5.1.x", "@turf/polygon-to-line": "5.1.x", "@turf/polygonize": "5.1.x", "@turf/projection": "5.1.x", "@turf/random": "5.1.x", "@turf/rewind": "5.1.x", "@turf/rhumb-bearing": "5.1.x", "@turf/rhumb-destination": "5.1.x", "@turf/rhumb-distance": "5.1.x", "@turf/sample": "5.1.x", "@turf/sector": "5.1.x", "@turf/shortest-path": "5.1.x", "@turf/simplify": "5.1.x", "@turf/square": "5.1.x", "@turf/square-grid": "5.1.x", "@turf/standard-deviational-ellipse": "5.1.x", "@turf/tag": "5.1.x", "@turf/tesselate": "5.1.x", "@turf/tin": "5.1.x", "@turf/transform-rotate": "5.1.x", "@turf/transform-scale": "5.1.x", "@turf/transform-translate": "5.1.x", "@turf/triangle-grid": "5.1.x", "@turf/truncate": "5.1.x", "@turf/union": "5.1.x", "@turf/unkink-polygon": "5.1.x", "@turf/voronoi": "5.1.x"}}, "node_modules/@turf/union": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "turf-jsts": "*"}}, "node_modules/@turf/unkink-polygon": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/area": "^5.1.5", "@turf/boolean-point-in-polygon": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5", "rbush": "^2.0.1"}}, "node_modules/@turf/voronoi": {"version": "5.1.5", "license": "MIT", "dependencies": {"@turf/helpers": "^5.1.5", "@turf/invariant": "^5.1.5", "d3-voronoi": "1.1.2"}}, "node_modules/@types/geojson": {"version": "7946.0.16", "resolved": "https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz", "integrity": "sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==", "license": "MIT"}, "node_modules/accepts": {"version": "1.3.7", "license": "MIT", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/mime-db": {"version": "1.43.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/mime-types": {"version": "2.1.26", "license": "MIT", "dependencies": {"mime-db": "1.43.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/agent-base": {"version": "7.1.0", "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/agent-base/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/agent-base/node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/ajv": {"version": "6.12.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "node_modules/append-field": {"version": "1.0.0", "license": "MIT"}, "node_modules/array-flatten": {"version": "1.1.1", "license": "MIT"}, "node_modules/array-parallel": {"version": "0.1.3", "license": "MIT"}, "node_modules/array-series": {"version": "0.1.5", "license": "MIT"}, "node_modules/asn1": {"version": "0.2.4", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-options": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/assert-options/-/assert-options-0.8.0.tgz", "integrity": "sha512-qSELrEaEz4sGwTs4Qh+swQkjiHAysC4rot21+jzXU86dJzNG+FDqBzyS3ohSoTRf4ZLA3FSwxQdiuNl5NXUtvA==", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "3.2.0", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/aws-sign2": {"version": "0.7.0", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.9.1", "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/block-stream2": {"version": "2.0.0", "license": "MIT", "dependencies": {"readable-stream": "^3.4.0"}}, "node_modules/block-stream2/node_modules/readable-stream": {"version": "3.6.0", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/body-parser": {"version": "1.19.0", "license": "MIT", "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/buffer-writer": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/buffer-writer/-/buffer-writer-2.0.0.tgz", "integrity": "sha512-a7ZpuTZU1TRtnwyCNW3I5dc0wWNC3VR9S++Ewyk2HHZdrO3CQJqSpd+95Us590V6AL7JqUAH2IwZ/398PmNFgw==", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/busboy": {"version": "1.6.0", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/bytes": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/caseless": {"version": "0.12.0", "license": "Apache-2.0"}, "node_modules/color": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/color/-/color-4.2.3.tgz", "integrity": "sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==", "license": "MIT", "dependencies": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}, "engines": {"node": ">=12.5.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz", "integrity": "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.0", "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/concaveman": {"version": "1.1.1", "license": "ISC", "dependencies": {"monotone-convex-hull-2d": "^1.0.1", "point-in-polygon": "^1.0.1", "rbush": "^2.0.1", "robust-orientation": "^1.1.3", "tinyqueue": "^1.1.0"}}, "node_modules/content-disposition": {"version": "0.5.3", "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.4.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "license": "MIT"}, "node_modules/core-util-is": {"version": "1.0.2", "license": "MIT"}, "node_modules/cross-spawn": {"version": "4.0.2", "license": "MIT", "dependencies": {"lru-cache": "^4.0.1", "which": "^1.2.9"}}, "node_modules/d3-array": {"version": "1.2.4", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-geo": {"version": "1.7.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "1"}}, "node_modules/d3-voronoi": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/dashdash": {"version": "1.14.1", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/deep-equal": {"version": "1.0.1", "license": "MIT"}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/density-clustering": {"version": "1.3.0", "license": "MIT"}, "node_modules/depd": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/destroy": {"version": "1.0.4", "license": "MIT"}, "node_modules/double-ended-queue": {"version": "2.1.0-0", "license": "MIT"}, "node_modules/earcut": {"version": "2.1.5", "license": "ISC"}, "node_modules/ecc-jsbn": {"version": "0.1.2", "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/es6-error": {"version": "4.1.1", "license": "MIT"}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express": {"version": "4.17.1", "license": "MIT", "dependencies": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/extend": {"version": "3.0.2", "license": "MIT"}, "node_modules/extsprintf": {"version": "1.3.0", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.1", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fast-xml-parser": {"version": "5.2.5", "resolved": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-5.2.5.tgz", "integrity": "sha512-pfX9uG9Ki0yekDHx2SiuRIyFdyAr1kMIMitPvb0YBo8SUfKvia7w7FIyd/l6av85pFYRhZscS75MwMnbvY+hcQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT", "dependencies": {"strnum": "^2.1.0"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/finalhandler": {"version": "1.1.2", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/forever-agent": {"version": "0.6.1", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.3.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/forwarded": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/geodesy": {"version": "1.1.3", "license": "MIT"}, "node_modules/geojson-equality": {"version": "0.1.6", "license": "MIT", "dependencies": {"deep-equal": "^1.0.0"}}, "node_modules/geojson-rbush": {"version": "2.1.0", "license": "MIT", "dependencies": {"@turf/helpers": "*", "@turf/meta": "*", "rbush": "*"}}, "node_modules/get-closest": {"version": "0.0.4"}, "node_modules/getpass": {"version": "0.1.7", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/gm": {"version": "1.23.1", "license": "MIT", "dependencies": {"array-parallel": "~0.1.3", "array-series": "~0.1.5", "cross-spawn": "^4.0.0", "debug": "^3.1.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/gm/node_modules/debug": {"version": "3.2.6", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/gm/node_modules/ms": {"version": "2.1.1", "license": "MIT"}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/har-schema": {"version": "2.0.0", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.3", "license": "MIT", "dependencies": {"ajv": "^6.5.5", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/http-errors": {"version": "1.7.2", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-signature": {"version": "1.2.0", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/https-proxy-agent": {"version": "7.0.2", "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/https-proxy-agent/node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arrayish": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==", "license": "MIT"}, "node_modules/is-typedarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isstream": {"version": "0.1.2", "license": "MIT"}, "node_modules/jsbn": {"version": "0.1.1", "license": "MIT"}, "node_modules/json-schema": {"version": "0.2.3"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/json-stream": {"version": "1.0.0", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "license": "ISC"}, "node_modules/jsonwebtoken": {"version": "9.0.2", "resolved": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "integrity": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jsonwebtoken/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/jsprim": {"version": "1.4.1", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "node_modules/jwa": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/jwa/-/jwa-1.4.2.tgz", "integrity": "sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw==", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==", "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/lineclip": {"version": "1.1.5", "license": "ISC"}, "node_modules/lodash": {"version": "4.17.15", "license": "MIT"}, "node_modules/lodash.includes": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==", "license": "MIT"}, "node_modules/lodash.isinteger": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==", "license": "MIT"}, "node_modules/lodash.isnumber": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==", "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==", "license": "MIT"}, "node_modules/lru-cache": {"version": "4.1.5", "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mgrs": {"version": "1.0.0", "license": "MIT"}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.38.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.22", "license": "MIT", "dependencies": {"mime-db": "~1.38.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimist": {"version": "0.0.8", "license": "MIT"}, "node_modules/minio": {"version": "7.0.14", "license": "Apache-2.0", "dependencies": {"async": "^3.1.0", "block-stream2": "^2.0.0", "es6-error": "^4.1.1", "json-stream": "^1.0.0", "lodash": "^4.14.2", "mime-types": "^2.1.14", "mkdirp": "^0.5.1", "querystring": "0.2.0", "through2": "^3.0.1", "xml": "^1.0.0", "xml2js": "^0.4.15"}, "engines": {"node": ">= 4"}}, "node_modules/mkdirp": {"version": "0.5.1", "license": "MIT", "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/monotone-convex-hull-2d": {"version": "1.0.1", "license": "MIT", "dependencies": {"robust-orientation": "^1.1.3"}}, "node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/multer": {"version": "1.4.5-lts.1", "license": "MIT", "dependencies": {"append-field": "^1.0.0", "busboy": "^1.0.0", "concat-stream": "^1.5.2", "mkdirp": "^0.5.4", "object-assign": "^4.1.1", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/multer/node_modules/minimist": {"version": "1.2.7", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/multer/node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/negotiator": {"version": "0.6.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/node-fetch": {"version": "2.6.11", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/oauth-sign": {"version": "0.9.0", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/packet-reader": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/packet-reader/-/packet-reader-1.0.0.tgz", "integrity": "sha512-HAKu/fG3HpHFO0AA8WE8q2g+gBJaZ9MG7fcKk+IJPLTGAD6Psw4443l+9DGRbOIh3/aXr7Phy0TjilYivJo5XQ==", "license": "MIT"}, "node_modules/papaparse": {"version": "5.4.1", "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-to-regexp": {"version": "0.1.7", "license": "MIT"}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT"}, "node_modules/pg": {"version": "8.8.0", "resolved": "https://registry.npmjs.org/pg/-/pg-8.8.0.tgz", "integrity": "sha512-UXYN0ziKj+AeNNP7VDMwrehpACThH7LUl/p8TDFpEUuSejCUIwGSfxpHsPvtM6/WXFy6SU4E5RG4IJV/TZAGjw==", "license": "MIT", "dependencies": {"buffer-writer": "2.0.0", "packet-reader": "1.0.0", "pg-connection-string": "^2.5.0", "pg-pool": "^3.5.2", "pg-protocol": "^1.5.0", "pg-types": "^2.1.0", "pgpass": "1.x"}, "engines": {"node": ">= 8.0.0"}, "peerDependencies": {"pg-native": ">=3.0.1"}, "peerDependenciesMeta": {"pg-native": {"optional": true}}}, "node_modules/pg-connection-string": {"version": "2.9.1", "resolved": "https://registry.npmjs.org/pg-connection-string/-/pg-connection-string-2.9.1.tgz", "integrity": "sha512-nkc6NpDcvPVpZXxrreI/FOtX3XemeLl8E0qFr6F2Lrm/I8WOnaWNhIPK2Z7OHpw7gh5XJThi6j6ppgNoaT1w4w==", "license": "MIT"}, "node_modules/pg-int8": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/pg-int8/-/pg-int8-1.0.1.tgz", "integrity": "sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==", "license": "ISC", "engines": {"node": ">=4.0.0"}}, "node_modules/pg-minify": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/pg-minify/-/pg-minify-1.6.2.tgz", "integrity": "sha512-1KdmFGGTP6jplJoI8MfvRlfvMiyBivMRP7/ffh4a11RUFJ7kC2J0ZHlipoKiH/1hz+DVgceon9U2qbaHpPeyPg==", "license": "MIT", "engines": {"node": ">=8.0"}}, "node_modules/pg-pool": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/pg-pool/-/pg-pool-3.10.1.tgz", "integrity": "sha512-Tu8jMlcX+9d8+QVzKIvM/uJtp07PKr82IUOYEphaWcoBhIYkoHpLXN3qO59nAI11ripznDsEzEv8nUxBVWajGg==", "license": "MIT", "peerDependencies": {"pg": ">=8.0"}}, "node_modules/pg-promise": {"version": "10.15.4", "resolved": "https://registry.npmjs.org/pg-promise/-/pg-promise-10.15.4.tgz", "integrity": "sha512-BKlHCMCdNUmF6gagVbehRWSEiVcZzPVltEx14OJExR9Iz9/1R6KETDWLLGv2l6yRqYFnEZZy1VDjRhArzeIGrw==", "license": "MIT", "dependencies": {"assert-options": "0.8.0", "pg": "8.8.0", "pg-minify": "1.6.2", "spex": "3.2.0"}, "engines": {"node": ">=12.0"}}, "node_modules/pg-protocol": {"version": "1.10.2", "resolved": "https://registry.npmjs.org/pg-protocol/-/pg-protocol-1.10.2.tgz", "integrity": "sha512-Ci7jy8PbaWxfsck2dwZdERcDG2A0MG8JoQILs+uZNjABFuBuItAZCWUNz8sXRDMoui24rJw7WlXqgpMdBSN/vQ==", "license": "MIT"}, "node_modules/pg-types": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/pg-types/-/pg-types-2.2.0.tgz", "integrity": "sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==", "license": "MIT", "dependencies": {"pg-int8": "1.0.1", "postgres-array": "~2.0.0", "postgres-bytea": "~1.0.0", "postgres-date": "~1.0.4", "postgres-interval": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/pgpass": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/pgpass/-/pgpass-1.0.5.tgz", "integrity": "sha512-FdW9r/jQZhSeohs1Z3sI1yxFQNFvMcnmfuj4WBMUTxOrAyLMaTcE1aAMBiTlbMNaXvBCQuVi0R7hd8udDSP7ug==", "license": "MIT", "dependencies": {"split2": "^4.1.0"}}, "node_modules/point-in-polygon": {"version": "1.0.1", "license": "MIT"}, "node_modules/point-in-polygon-hao": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/point-in-polygon-hao/-/point-in-polygon-hao-1.2.4.tgz", "integrity": "sha512-x2pcvXeqhRHlNRdhLs/tgFapAbSSe86wa/eqmj1G6pWftbEs5aVRJhRGM6FYSUERKu0PjekJzMq0gsI2XyiclQ==", "license": "MIT", "dependencies": {"robust-predicates": "^3.0.2"}}, "node_modules/postgres-array": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/postgres-array/-/postgres-array-2.0.0.tgz", "integrity": "sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/postgres-bytea": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/postgres-bytea/-/postgres-bytea-1.0.0.tgz", "integrity": "sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/postgres-date": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/postgres-date/-/postgres-date-1.0.7.tgz", "integrity": "sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/postgres-interval": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/postgres-interval/-/postgres-interval-1.2.0.tgz", "integrity": "sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==", "license": "MIT", "dependencies": {"xtend": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/proj4": {"version": "2.9.0", "license": "MIT", "dependencies": {"mgrs": "1.0.0", "wkt-parser": "^1.3.1"}}, "node_modules/proper-lockfile": {"version": "4.1.2", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "retry": "^0.12.0", "signal-exit": "^3.0.2"}}, "node_modules/proxy-addr": {"version": "2.0.6", "license": "MIT", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/pseudomap": {"version": "1.0.2", "license": "ISC"}, "node_modules/psl": {"version": "1.7.0", "license": "MIT"}, "node_modules/punycode": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.7.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/querystring": {"version": "0.2.0", "engines": {"node": ">=0.4.x"}}, "node_modules/quickselect": {"version": "1.1.1", "license": "ISC"}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.4.0", "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/rbush": {"version": "2.0.2", "license": "MIT", "dependencies": {"quickselect": "^1.0.1"}}, "node_modules/readable-stream": {"version": "2.3.6", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/redis": {"version": "2.8.0", "license": "MIT", "dependencies": {"double-ended-queue": "^2.1.0-0", "redis-commands": "^1.2.0", "redis-parser": "^2.6.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/redis-commands": {"version": "1.4.0", "license": "MIT"}, "node_modules/redis-parser": {"version": "2.6.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/request": {"version": "2.88.2", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/request/node_modules/qs": {"version": "6.5.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/retry": {"version": "0.12.0", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/robust-orientation": {"version": "1.1.3", "license": "MIT", "dependencies": {"robust-scale": "^1.0.2", "robust-subtract": "^1.0.0", "robust-sum": "^1.0.0", "two-product": "^1.0.2"}}, "node_modules/robust-predicates": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/robust-predicates/-/robust-predicates-3.0.2.tgz", "integrity": "sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==", "license": "Unlicense"}, "node_modules/robust-scale": {"version": "1.0.2", "license": "MIT", "dependencies": {"two-product": "^1.0.2", "two-sum": "^1.0.0"}}, "node_modules/robust-subtract": {"version": "1.0.0", "license": "MIT"}, "node_modules/robust-sum": {"version": "1.0.0", "license": "MIT"}, "node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sax": {"version": "1.2.4", "license": "ISC"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "0.17.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "license": "MIT"}, "node_modules/serve-static": {"version": "1.14.1", "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.1.1", "license": "ISC"}, "node_modules/sharp": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/sharp/-/sharp-0.34.2.tgz", "integrity": "sha512-lszvBmB9QURERtyKT2bNmsgxXK0ShJrL/fvqlonCo7e6xBF8nT8xU6pW+PMIbLsz0RxQk3rgH9kd8UmvOzlMJg==", "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"color": "^4.2.3", "detect-libc": "^2.0.4", "semver": "^7.7.2"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "0.34.2", "@img/sharp-darwin-x64": "0.34.2", "@img/sharp-libvips-darwin-arm64": "1.1.0", "@img/sharp-libvips-darwin-x64": "1.1.0", "@img/sharp-libvips-linux-arm": "1.1.0", "@img/sharp-libvips-linux-arm64": "1.1.0", "@img/sharp-libvips-linux-ppc64": "1.1.0", "@img/sharp-libvips-linux-s390x": "1.1.0", "@img/sharp-libvips-linux-x64": "1.1.0", "@img/sharp-libvips-linuxmusl-arm64": "1.1.0", "@img/sharp-libvips-linuxmusl-x64": "1.1.0", "@img/sharp-linux-arm": "0.34.2", "@img/sharp-linux-arm64": "0.34.2", "@img/sharp-linux-s390x": "0.34.2", "@img/sharp-linux-x64": "0.34.2", "@img/sharp-linuxmusl-arm64": "0.34.2", "@img/sharp-linuxmusl-x64": "0.34.2", "@img/sharp-wasm32": "0.34.2", "@img/sharp-win32-arm64": "0.34.2", "@img/sharp-win32-ia32": "0.34.2", "@img/sharp-win32-x64": "0.34.2"}}, "node_modules/sharp/node_modules/detect-libc": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==", "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "3.0.2", "license": "ISC"}, "node_modules/simple-swizzle": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "integrity": "sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/skmeans": {"version": "0.9.7", "license": "MIT"}, "node_modules/spex": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/spex/-/spex-3.2.0.tgz", "integrity": "sha512-9srjJM7NaymrpwMHvSmpDeIK5GoRMX/Tq0E8aOlDPS54dDnDUIp30DrP9SphMPEETDLzEM9+4qo+KipmbtPecg==", "license": "MIT", "engines": {"node": ">=4.5"}}, "node_modules/split2": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz", "integrity": "sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==", "license": "ISC", "engines": {"node": ">= 10.x"}}, "node_modules/sshpk": {"version": "1.16.1", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/streamsearch": {"version": "1.1.0", "engines": {"node": ">=10.0.0"}}, "node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/strnum": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/strnum/-/strnum-2.1.1.tgz", "integrity": "sha512-7ZvoFTiCnGxBtDqJ//Cu6fWtZtc7Y3x+QOirG15wztbdngGSkht27o2pyGWrVy0b4WAy3jbKmnoK6g5VlVNUUw==", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT"}, "node_modules/sweepline-intersections": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/sweepline-intersections/-/sweepline-intersections-1.5.0.tgz", "integrity": "sha512-AoVmx72QHpKtItPu72TzFL+kcYjd67BPLDoR0LarIk+xyaRg+pDTMFXndIEvZf9xEKnJv6JdhgRMnocoG0D3AQ==", "license": "MIT", "dependencies": {"tinyqueue": "^2.0.0"}}, "node_modules/sweepline-intersections/node_modules/tinyqueue": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/tinyqueue/-/tinyqueue-2.0.3.tgz", "integrity": "sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==", "license": "ISC"}, "node_modules/through2": {"version": "3.0.1", "license": "MIT", "dependencies": {"readable-stream": "2 || 3"}}, "node_modules/tinyqueue": {"version": "1.2.3", "license": "ISC"}, "node_modules/toidentifier": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/topojson-client": {"version": "3.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"commander": "2"}, "bin": {"topo2geo": "bin/topo2geo", "topomerge": "bin/topomerge", "topoquantize": "bin/topoquantize"}}, "node_modules/topojson-server": {"version": "3.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"commander": "2"}, "bin": {"geo2topo": "bin/geo2topo"}}, "node_modules/tough-cookie": {"version": "2.5.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/turf-jsts": {"version": "1.2.3", "license": "(EDL-1.0 OR EPL-1.0)"}, "node_modules/tweetnacl": {"version": "0.14.5", "license": "Unlicense"}, "node_modules/two-product": {"version": "1.0.2", "license": "MIT"}, "node_modules/two-sum": {"version": "1.0.0", "license": "MIT"}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/type-is/node_modules/mime-db": {"version": "1.43.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/type-is/node_modules/mime-types": {"version": "2.1.26", "license": "MIT", "dependencies": {"mime-db": "1.43.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray": {"version": "0.0.6", "license": "MIT"}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/uri-js": {"version": "4.2.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url": {"version": "0.11.0", "license": "MIT", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "node_modules/url/node_modules/punycode": {"version": "1.3.2", "license": "MIT"}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "3.4.0", "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/valid-url": {"version": "1.0.9"}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/wasm-vips": {"version": "0.0.13", "resolved": "https://registry.npmjs.org/wasm-vips/-/wasm-vips-0.0.13.tgz", "integrity": "sha512-aQT7pF8CNGEO9YLbcMZ2WLalnZsSO4ZoLAvVn4FUWr0vdqcpDCAYPadTxSeGpaLKlGr4wD22jdcDxwSKUM5g9w==", "license": "MIT", "engines": {"node": ">=16.4.0"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "1.3.1", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/wkt-parser": {"version": "1.3.3", "license": "MIT"}, "node_modules/write-file-atomic": {"version": "5.0.1", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^4.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/write-file-atomic/node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/xml": {"version": "1.0.1", "license": "MIT"}, "node_modules/xml2js": {"version": "0.4.23", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xmlbuilder": {"version": "11.0.1", "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/xtend": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/yallist": {"version": "2.1.2", "license": "ISC"}}}